
#!/bin/bash
current_dir=$(dirname "$(realpath "$0")")
# echo "$current_dir"
parent_dir=$(dirname "$current_dir")
cd "$parent_dir"
source venv/bin/activate
# 判断 创建 log 目录
if [ ! -d "log" ]; then
    mkdir log
fi

# 判断创建  如果存在 test.pid 文件 先 kill 进程
if [ -f .test.pid.txt ]; then
    pid=$(cat .test.pid.txt)
    kill $pid
fi

echo :>.test.pid.txt
python atlas_server.py > ./log/web_start_run.log 2>&1 &
echo $! >.test.pid.txt

echo "Started"
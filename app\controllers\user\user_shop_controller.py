# -*- coding: utf-8 -*-
"""python 用户店铺操作"""
from app.controllers.user.base_user_controller import BaseUserController
from app.models.amazon_shop_model import AmazonShopCountryModel, UserVitualShopModel, \
    UserVirtualShopCountryRelationModel, AmazonShopSpInfoModel
from app.tps.amazon import AmazonShop
from app.logger import Logger
from app.utils.function import get_datetime
from app.utils.function import get_random_code


class UserShopController(BaseUserController):
    """用户店铺操作"""

    logger = Logger.get_instance("amazon_shop_outh")

    async def get_user_virtual_shop(self):
        """获取用户虚拟店铺
        # todo 因为以关系为准,还需要没有关系的店铺
        """
        data = await self.get_user_shops_info(user_id=self.request.state.login_user_id)
        if not data:
            return self.r(code=200, msg="Success", data=[])
        return self.r(code=200, msg="Success", data=data)

    async def add_user_virtual_shop(self):
        """添加用户虚拟店铺， 这里是 SP授权的时候添加，
            返回 亚马逊 跳转URL
        """
        post_data = await self.request.json()
        self.logger.info(f"new_auth sp: {post_data}")
        shop_name = post_data.get("shop_name")
        shop_id = post_data.get("shop_id")
        if not shop_name and not shop_id:
            return self.r(code=104001, msg="The store name or store ID cannot be empty")
        # 如果是
        country_code = post_data.get("country_code")
        if not country_code:
            return self.r(code=104002, msg="The country of the store cannot be empty")
        area_code = post_data.get("area_code")
        if not area_code:
            return self.r(code=104003, msg="The area code of the store cannot be empty")
        virtual_shop_vendor = post_data.get("virtual_shop_vendor", 0)
        # 添加店铺 或者更新店铺
        user_id = self.request.state.login_user_id
        sp_info = self._get_amazon_sp_url_and_code(
            user_id, area_code, virtual_shop_vendor, country_code)
        if virtual_shop_vendor and len(country_code.split(',')) != 1:
            return self.r(code=104006, msg="The country of the store only one")
        if not sp_info:
            return self.r(code=104005, msg="Get amazon sp url  failed, please try again")
        jump_url = sp_info.pop('jump_url')
        if shop_id:
            # 查询并更新
            shop_info = await UserVitualShopModel.get_one_conditions(
                conditions={
                    "id": shop_id,
                    "user_id": user_id
                }
            )
            if not shop_info:
                return self.r(code=104004, msg="The store does not exist")
            # 更新店铺
            update_data = {
                "shop_name": shop_name if shop_name else shop_info['shop_name'],
                "sp_auth_temp_country": country_code,
                "sp_auth_temp_area": area_code,
            }
            update_data.update(sp_info)
            result = await UserVitualShopModel.update_conditions(
                conditions={
                    "id": shop_id
                },
                new_values=update_data
            )
        else:
            # 添加店铺
            result = await UserVitualShopModel.add_one(
                data={
                    "user_id": user_id,
                    "shop_name": shop_name,
                    "sp_auth_temp_country": country_code,
                    "sp_auth_temp_area": area_code,
                    "virtual_shop_vendor": virtual_shop_vendor,
                    "add_datetime": get_datetime(),
                    **sp_info
                }
            )
        if result:
            return self.r(code=200, msg="Update successful", data=jump_url)

        return self.r(code=104005, msg="Get amazon url failed, please try again")

    def _get_amazon_sp_url_and_code(self, user_id: int, area_code: str,
                                    shop_is_vendor: int = 0, country_code: str = ''):
        """定义获取亚马逊SP 授权url"""
        now_time_stamp = get_datetime('timestamp')
        expire_timestamp = now_time_stamp + 3600
        # 生成随机 state，code
        random_code = get_random_code(64)
        random_state = str(user_id) + "_" + random_code + \
            "_" + str(expire_timestamp)

        # 生成跳转地址
        result, jump_url = AmazonShop.get_sp_auth_url(
            area_code=area_code,
            shop_is_vendor=shop_is_vendor,
            state=random_state,
            country_code=country_code
        )
        if not result:
            return False

        return {
            "sp_auth_temp_state": random_code,
            "sp_auth_temp_area": area_code,
            "sp_auth_temp_state_expire_timestamp": expire_timestamp,
            "jump_url": jump_url
        }

    async def get_ad_amazon_auth(self, ):
        """获取广告授权url, 在 已知 SP情况下授权,授权是根据 SP 授权的地区"""
        # 对应用户 的 国家授权列表
        post_data = await self.request.json()
        self.logger.info(f"new_ad_amazon_auth: {post_data}")
        sp_info_id = post_data.get("sp_info_id")
        virtual_shop_id = post_data.get("virtual_shop_id")
        if not sp_info_id or not virtual_shop_id:
            return self.r(code=104011, msg="The amazon shop sp id or virtual shop id error")
        # 查询Shop_sp 信息
        shop_sp_info = await AmazonShopSpInfoModel.get_one_conditions(
            conditions={
                "id": sp_info_id,
            }
        )
        if not shop_sp_info:
            return self.r(code=104011, msg="The amazon shop sp id error")
        # 获取已经有的国家
        countrys = await AmazonShopCountryModel.get_conditions(
            conditions={
                "shop_sp_info_id": sp_info_id
            }
        )
        country_ids = [item['id'] for item in countrys]
        # 验证店铺
        shop_info = await UserVitualShopModel.get_one_conditions(
            conditions={
                "id": virtual_shop_id,
                "user_id": self.request.state.login_user_id
            }
        )
        if not shop_info:
            return self.r(code=104011, msg="The amazon shop countrys does not exist")
        # 验证关系
        relation_count = await UserVirtualShopCountryRelationModel.get_conditions_count(
            conditions={
                "user_id": self.request.state.login_user_id,
                "virtual_shop_id": virtual_shop_id,
                "shop_country_id": ['in', country_ids]
            }
        )
        if not relation_count:
            return self.r(code=104011, msg="The amazon shop countrys relation does not exist")

        # 生成跳转 地址
        area_code = shop_sp_info['area_code']
        expire_timestamp = get_datetime('timestamp') + 3600
        # 生成随机 state code
        random_code = get_random_code(64)
        random_state = str(self.request.state.login_user_id) + "_" + random_code + \
            "_" + str(expire_timestamp)+"_" + str(sp_info_id)
        # 更新 到 虚拟店铺信息 ad中
        update_data = {
            "ad_auth_temp_state": random_code,
            "ad_auth_temp_area": area_code,
            "ad_auth_temp_state_expire_timestamp": expire_timestamp,
            "temp_sp_id_for_ad_auth": sp_info_id
        }
        # 更新 到 虚拟店铺信息 ad中
        result = await UserVitualShopModel.update_conditions(
            conditions={
                "id": virtual_shop_id
            },
            new_values=update_data
        )
        if not result:
            return self.r(code=104005, msg="Update shop info failed, please try again")
        # 生成跳转链接 AMAZON_AD_RETURN_URL
        result, jump_url = AmazonShop.get_ad_auth_url(
            area_code=area_code,
            state=random_state,
            shop_is_vendor=shop_info['virtual_shop_vendor'],
            country_code=shop_info['sp_auth_temp_country']
        )
        if not result:
            return self.r(code=104005, msg="Update shop info failed, please try again")
        return self.r(code=200, msg="successful", data=jump_url)

    async def delete_user_virtual_shop(self):
        """删除用户虚拟店铺"""

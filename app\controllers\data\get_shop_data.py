# -*- coding: utf-8 -*-
"""店铺基础信息 获取店铺信息 店铺ID 以 SP_ID 为基础"""
from app.controllers.data.base_data import BaseData
from app.models.amazon_shop_model import AmazonShopSpInfoModel, AmazonShopCountryModel


class GetShopData(BaseData):
    """读取店铺信息"""

    @BaseData.verify
    async def get_shop_list(self):
        """获取店铺列表"""
        # 获取参数
        post_data = await self.request.json()
        where = {"shop_ad_info_id": ['>', 0]}  # ad 授权的s s
        begin_time = post_data.get('AfterChangeDateTime', False)
        if "UID" in post_data:
            where["id"] = post_data["UID"]
        if 'CloseFlag' in post_data:
            where['is_close'] = post_data['CloseFlag']
        if begin_time:
            where["first_auth_datetime"] = [">", begin_time]

        shop_list = await AmazonShopSpInfoModel.get_sp_ad_info(
            conditions=where
        )
        if not shop_list:
            return self.r()

        # 获取所有店铺
        shop_ids = [shop['id'] for shop in shop_list]
        # 查询所有国家
        all_countrys = await AmazonShopCountryModel.get_conditions(
            conditions={"shop_sp_info_id": ["in", shop_ids]}
        )

        auth_user_key = {}
        for country in all_countrys:
            auth_user_key.setdefault(country['shop_sp_info_id'], {
                "SP": [], "AD": []
            })
            auth_user_key[country['shop_sp_info_id']
                          ]["SP"].append(country['country_code'])

        # 添加到店铺
        result_data = []
        for shop in shop_list:
            result = {}
            result['ID'] = shop['id']
            result['ShopName'] = "GJ-"+str(shop['id'])
            result['DbName'] = shop['local_db_name']
            result['LogDbName'] = shop['local_log_db_name']
            result['CloseFlag'] = shop['is_close']
            result['IsDistributor'] = shop['shop_is_vendor']
            result['IsNewAmazonApp'] = False
            result['RegDatetime'] = shop['first_auth_datetime']
            result['AuthData'] = auth_user_key.get(shop['id'], {
                "SP": [], "AD": []
            })
            result['AuthData']['AD'].append(shop['ad_area_code'])
            result_data.append(result)

        return self.r(data=result_data)

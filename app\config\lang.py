# -*- coding: utf-8 -*-
"""语言包
类型 说明
标识符号 1位  板块 2位 错误码 3位
1 用户
    01 注册 101001 邮箱格式不正确
    01 注册 101002 邮箱发送失败
    02 登录 102001 登录失败 账号密码错误
    02 登录 102002 登录 超时或者没有token 
    02 登录更新密码 102003 更新密码失败 
    02 重置/忘记密码 102004 不存在的邮箱
    02 重置/忘记密码 102005 更新密码失败
    02 重置/忘记密码 102006 重置密码链接失效
    03 google联合登录 103001 google 邮箱未验证
    03 google 联合登录失败 103002 google 联合登录失败
    04 店铺授权 104001 店铺名称或者店铺ID不能为空
    04 店铺授权 104002 店铺国家不能为空
    04 店铺授权 104003 店铺地区不能为空
    04 店铺授权 104004 店铺店铺不存在
    04 店铺授权 104005 获取店铺授权url失败
    04 店铺授权 104006 获取店铺授权url失败 请重试
    04 店铺授权回调 104007 SP店铺授权回调失败-参数异常
    04 店铺授权回调 104008 SP店铺授权回调失败-店铺匹配异常
    04 店铺授权回调 104009 SP店铺授权回调失败-SP回调获取accesstoken失败
    04 店铺授权回调 104010 SP店铺授权回调失败-店铺信息异常
    04 店铺授权回调 104011 AD授权-参数错误
    04 店铺授权回调 104012 AD授权-验证返回信息失败
    04 店铺授权回调 104013 AD授权-获取ad  accesstoken 失败
    04 店铺授权回调 104014 AD授权-店铺 AD 和SP 不一致
    04 店铺授权回调 104015 AD授权-更新店铺信息失败
    04 店铺授权邀请 104016 邮件邀请码 不正确
    04 店铺授权邀请 104017 点击邮件 和当前登录的用户不一致
    04 店铺授权邀请 104018 点击邮件 同一个登录用户更新权限失败
    04 店铺授权邀请 104019 点击邮件 同一个登录用户更新权限成功
    05 用户权限     105001 用户权限已经申请(处于申请中)
    05 用户权限     105002 没有店铺管理员，无法申请权限
    05 用户权限     105003 已经是管理员了,不用申请
    05 用户权限     105004 请选择申请状态异常，参数应该是协同或者只读
    05 用户权限     105005 你已经是对应权限了
    05 用户权限     105006  撤销权限申请异常，无对应记录
    05 用户权限     105007  撤销权限申请异常，已经被处理 不能
    05 用户权限     105008  撤销权限申请失败, 更新数据错误
    05 用户权限     105009  变更权限参数错误 
    05 用户权限     105010  变更权限参数错误 非管理员不可操作
    05 用户权限     105011  变更权限 被操作权限不存在
    05 用户权限     105012  变更权限 变更更新权限数据失败
    05 用户权限     105013  变更权限 更新用户全新 需要给新的不一样的权限
    05 用户权限     105014  变更权限 该账号非当前 国家用户 不能变更权限
    05 用户权限     105015  变更权限 该账号非当前 交换管理员更新数据失败
2 管理员
3 系统 
   00 系统统一错误
      参数错误 300001 参数错误
      发送邮件失败 300002 发送邮件失败
   01 验证相关 
      验证码  301001 验证码验证失败
4 数据接口
"""

SYS_LANG_CODE = {
    "EN": {
        "200": "Success",
        "400": "Bad Request",
        "401": "Unauthorized",
        "403": "Forbidden",
        "404": "Not Found",
        "405": "Method Not Allowed",
        "406": "Not Acceptable",
        "409": "Conflict",
        "410": "Gone",
        "411": "Length Required",
        "413": "Request Entity Too Large",
        "414": "Request-URI Too Long",
        "415": "Unsupported Media Type",
        "416": "Requested Range Not Satisfiable",
        "417": "Expectation Failed",
        "500": "Internal Server Error",
        "501": "Not Implemented",
        "502": "Bad Gateway",
        "101001": "Email format is incorrect",
    },
    "CN": {
        "200": "成功",
        "400": "请求错误",
        "401": "未授权",
        "403": "拒绝访问",
        "404": "请求未找到",
        "405": "请求方法未允许",
        "406": "不接受",
        "409": "冲突",
    }
}

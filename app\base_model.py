# -*- coding: utf-8 -*-
"""基础公共模型"""
import datetime
from datetime import datetime, date, time
from decimal import Decimal
from uuid import UUID
from sqlalchemy import text, and_, func, or_, update, asc, desc, select, delete
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import declarative_base
from app.db import get_bound_db
from setting import TABLE_PREFIX

Base = declarative_base()


class BaseModel(Base):
    """基础公共模型"""
    # 不创建该类表
    __abstract__ = True
    table_prefix = TABLE_PREFIX
    _db = None

    @classmethod
    def db(cls):
        """获取数据库连接"""
        return get_bound_db()

    @classmethod
    async def query(cls, sql):
        """查询数据"""
        result = await cls.db().execute(text(sql))
        return [cls.to_dict(row) for row in result]

    @classmethod
    def columns(cls):
        """返回表所有字段"""
        return cls.__table__.columns.keys()

    @classmethod
    def to_dict(cls, obj, combin=False):
        """转换对象"""
        if not obj:
            return {}
        columns = cls.columns()
        item_dict = {}

        if not combin:
            for column in columns:
                value = getattr(obj, column)
                if isinstance(value, datetime):
                    item_dict[column] = value.strftime('%Y-%m-%d %H:%M:%S')
                elif isinstance(value, date):
                    item_dict[column] = value.strftime('%Y-%m-%d')
                elif isinstance(value, Decimal):
                    item_dict[column] = str(value)
                else:
                    item_dict[column] = value
        else:
            for column in columns:
                item_dict[column] = obj.get(column)
        return item_dict

    @classmethod
    def get_primary_key_field(cls):
        """获取主键字段对象（仅支持单主键）"""
        pk_columns = list(cls.__table__.primary_key.columns)
        if not pk_columns:
            return False
        return pk_columns[0].name

    @classmethod
    async def add_one(cls, data, is_transaction=False):
        """添加新记录"""
        try:
            new_record = cls(**data)
            cls.db().add(new_record)

            if not is_transaction:
                await cls.db().commit()
                # 刷新对象以获取数据库生成的值（如自增ID）
                await cls.db().refresh(new_record)
                pk_name = cls.get_primary_key_field()
                if pk_name:
                    return getattr(new_record, pk_name)
                return new_record
            # 对于事务模式，也需要flush以获取ID
            await cls.db().flush()
            pk_name = cls.get_primary_key_field()
            if pk_name:
                return getattr(new_record, pk_name)
            return new_record

        except SQLAlchemyError as e:
            print(f"Database error in add_one: {str(e)}")
            if not is_transaction:
                await cls.db().rollback()
            return False  # 重新抛出异常，让调用方决定如何处理
        except (ValueError, TypeError, AttributeError) as e:
            print(f"Data validation error in add_one: {str(e)}")
            # 这些异常通常是数据问题，不需要回滚数据库
            return False

    @classmethod
    async def add_multy(cls, data, is_transaction=False):
        """批量添加"""
        try:
            datas = [cls(**item) for item in data]
            cls.db().add_all(datas)
            if not is_transaction:
                await cls.db().commit()
            return len(data)
        except (ValueError, TypeError, AttributeError, SQLAlchemyError) as e:
            print(f"error {e}")
            if not is_transaction:
                await cls.db().rollback()
            return False

    @classmethod
    def make_filters(cls, conditions):
        """条件生成器"""
        haive_columns = cls.columns()
        filters = []

        # 将条件处理逻辑拆分为多个辅助方法
        for key, condition in conditions.items():
            if key.lower() == 'or_':
                filters.extend(cls._handle_or_condition(condition))
                continue

            if key not in haive_columns:
                continue

            field = getattr(cls, key)

            if isinstance(condition, (dict, list)):
                filter_item = cls._handle_complex_condition(field, condition)
                if filter_item is not None:
                    filters.append(filter_item)
            else:
                if condition is None:
                    filters.append(field.is_(None))
                else:
                    filters.append(field == str(condition))

        return filters

    @classmethod
    def _handle_or_condition(cls, condition):
        """处理OR条件"""
        or_filters = []
        for or_condition in condition:
            or_filters.extend(cls.make_filters(or_condition))

        return [or_(*or_filters)] if or_filters else []

    @classmethod
    def _handle_complex_condition(cls, field, condition):
        """处理复杂条件"""
        # 统一条件格式
        if isinstance(condition, dict) and 'operator' in condition and 'value' in condition:
            op = condition.get('operator')
            value = condition.get('value')
        elif len(condition) == 2:
            op = condition[0]
            value = condition[1]
        else:
            return None

        # 处理不同操作符
        operators = {
            '>': lambda f, v: f > v,
            '<': lambda f, v: f < v,
            '>=': lambda f, v: f >= v,
            '<=': lambda f, v: f <= v,
            '<>': lambda f, v: f != v,
            '!=': lambda f, v: f != v,
            'in': lambda f, v: f.in_(v if isinstance(v, list) else [v]),
            'like': lambda f, v: f.like('%'+str(v)+'%'),
            'ilike': lambda f, v: f.ilike('%'+str(v)+'%'),
            'between': lambda f, v: f.between(v[0], v[1])
        }

        return operators.get(op, lambda f, v: f.between(op, v))(field, value)

    @classmethod
    async def delete_conditions(cls, conditions, is_transaction=False):
        """根据条件进行删除"""
        filters = cls.make_filters(conditions=conditions)

        try:
            # 直接构建删除语句
            stmt = delete(cls).where(and_(*filters))
            result = await cls.db().execute(stmt)
            if not is_transaction:
                await cls.db().commit()

            # 返回删除的记录数
            return result.rowcount

        except SQLAlchemyError as e:
            print(f"Database error in delete_conditions: {str(e)}")
            # 只有在非事务模式下才回滚
            if not is_transaction:
                await cls.db().rollback()
            raise  # 重新抛出异常，让调用方处理
        except (ValueError, TypeError, AttributeError) as e:
            print(f"Data validation error in delete_conditions: {str(e)}")
            # 这些异常是数据问题，不需要数据库回滚
            raise  # 重新抛出异常

    @classmethod
    async def update_conditions(cls, conditions, new_values, is_transaction=False):
        """条件更新"""
        filters = cls.make_filters(conditions=conditions)
        try:
            stmt = (
                update(cls)
                .where(and_(*filters))
                .values(**new_values)
            )
            result = await cls.db().execute(stmt)
            if not is_transaction:
                await cls.db().commit()
            # 返回受影响的行数，而不是布尔值
            return result.rowcount
        except SQLAlchemyError as e:
            print(f"Database error in update_conditions: {str(e)}")
            # 只有在非事务模式下才回滚
            if not is_transaction:
                await cls.db().rollback()
            raise  # 重新抛出异常，让调用方处理
        except (ValueError, TypeError, AttributeError) as e:
            print(f"Data validation error in update_conditions: {str(e)}")
            # 这些异常是数据问题，不需要数据库回滚
            raise  # 重新抛出异常

    @classmethod
    async def get_one_conditions(cls, conditions, order_by: dict = None):
        """根据条件 和 order by{'id': 'desc'} 来获取一条数据"""
        try:
            filters = cls.make_filters(conditions=conditions)
            stmt = select(cls).where(and_(*filters))
            # 排序
            if order_by:
                for field_name, direction in order_by.items():
                    field = getattr(cls, field_name, None)
                    if field is not None:
                        stmt = stmt.order_by(
                            asc(field) if direction.lower() == "asc" else desc(field))
            result = await cls.db().execute(stmt)
            db_result = result.scalars().first()
            return cls.to_dict(db_result) if db_result else {}
        except SQLAlchemyError as e:
            print(f"[get_one_conditions error] {e}")
            return {}

    @classmethod
    async def get_conditions_count(cls, conditions):
        """返回符合条件的数量"""
        filters = cls.make_filters(conditions=conditions)
        count_stmt = select(func.count()).select_from(
            cls).where(and_(*filters))
        total_result = await cls.db().execute(count_stmt)
        total = total_result.scalar_one() or 0
        return total

    @classmethod
    async def get_conditions(cls, conditions, **other_config):
        """根据条件 返回数据，自定义返回字段"""
        return_fields = other_config.get("return_fields")
        key_field = other_config.get("key")
        order_by = other_config.get("order_by")
        limit = other_config.get("limit")

        try:
            filters = cls.make_filters(conditions=conditions)
            stmt = select(cls).where(and_(*filters))

            # 排序
            if order_by:
                for field_name, direction in order_by.items():
                    field = getattr(cls, field_name, None)
                    if field is not None:
                        stmt = stmt.order_by(
                            asc(field) if direction.lower() == "asc" else desc(field))

            # 限制
            if limit:
                stmt = stmt.limit(limit)

            result = await cls.db().execute(stmt)
            db_result = result.scalars().all()

        except SQLAlchemyError as e:
            print(f"[get_conditions error] {e}")
            db_result = []

        if not db_result:
            return {} if key_field else []

        # 转换为字典列表
        items = [cls.to_dict(item) for item in db_result]

        if key_field:
            return {
                item[key_field]: (
                    {field: item[field] for field in return_fields}
                    if return_fields else item
                )
                for item in items
            }

        return [
            {field: item[field] for field in return_fields}
            if return_fields else item
            for item in items
        ]

    @classmethod
    async def get_conditions_page(cls, conditions, **other_config):
        """ Pagination function """
        page = other_config.get('page', 1)
        page_size = other_config.get('page_size', 10)
        order_by = other_config.get("order_by")

        try:
            filters = cls.make_filters(conditions=conditions)
            count_stmt = select(func.count()).select_from(
                cls).where(and_(*filters))
            total_result = await cls.db().execute(count_stmt)
            total = total_result.scalar_one()

            total_pages = total // page_size
            stmt = select(cls).where(and_(*filters))
            # 排序
            if order_by:
                for field_name, direction in order_by.items():
                    field = getattr(cls, field_name, None)
                    if field is not None:
                        stmt = stmt.order_by(
                            asc(field) if direction.lower() == "asc" else desc(field))

            stmt = stmt.offset((page - 1) * page_size).limit(page_size)
            result = await cls.db().execute(stmt)
            records = result.scalars().all()
            items = [cls.to_dict(item) for item in records]
            return {
                "total": total,
                "total_pages": total_pages,
                "page": page,
                "page_size": page_size,
                "items": items
            }
        except SQLAlchemyError as e:
            print(f"[get_conditions error] {e}")
            return {
                "total": 0,
                "total_pages": 0,
                "page": 0,
                "page_size": 0,
                "items": []
            }

    @classmethod
    def serialize_for_json(cls, obj):
        """将对象转换为JSON可序列化的格式"""
        if isinstance(obj, (datetime, date, time)):
            return obj.isoformat()
        if isinstance(obj, Decimal):
            return float(obj)
        if isinstance(obj, UUID) or hasattr(obj, '__dict__'):
            return str(obj)
        if isinstance(obj, bytes):
            return obj.decode('utf-8', errors='ignore')
        if isinstance(obj, set):
            return list(obj)
        return obj

# -*- coding: utf-8 -*-
"""fastapi 调用redis 单例模式类 支持多线程异步调用读写"""
import threading
import asyncio
from typing import Optional, Any
import redis.asyncio as aioredis
import redis
from setting import REDIS_HOST, REDIS_PORT, REDIS_PSWD as REDIS_PASSWORD, REDIS_DB


class RedisFunction:
    """redis 单例模式类 - 支持多线程和异步操作"""
    _instance = None
    _lock = threading.Lock()
    _async_instance = None
    _async_lock = asyncio.Lock()

    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(RedisFunction, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化 - 只初始化一次"""
        if hasattr(self, '_initialized') and self._initialized:
            return

        # 同步Redis连接
        self.redis = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD if REDIS_PASSWORD else None,
            db=REDIS_DB,
            decode_responses=True,  # 自动解码响应
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )

        # 异步Redis连接池
        self._async_redis = None

        # 测试连接
        try:
            self.redis.ping()
        except (redis.ConnectionError, redis.TimeoutError) as e:
            raise Exception(f"Redis 连接失败: {e}")
        except redis.RedisError as e:
            raise Exception(f"Redis 其他错误: {e}")

        self._initialized = True

    async def get_async_redis(self) -> aioredis.Redis:
        """获取异步Redis连接"""
        if self._async_redis is None:
            async with RedisFunction._async_lock:
                if self._async_redis is None:
                    self._async_redis = aioredis.Redis(
                        host=REDIS_HOST,
                        port=REDIS_PORT,
                        password=REDIS_PASSWORD if REDIS_PASSWORD else None,
                        db=REDIS_DB,
                        decode_responses=True,
                        socket_connect_timeout=5,
                        socket_timeout=5,
                        retry_on_timeout=True,
                        health_check_interval=30
                    )
                    try:
                        await self._async_redis.ping()
                        print("Redis 异步连接成功")
                    except (aioredis.ConnectionError, aioredis.TimeoutError) as e:
                        print(f"Redis 异步连接失败: {e}")
                        raise Exception(f"Redis 异步连接失败: {e}") from e
                    except aioredis.RedisError as e:
                        print(f"Redis 异步其他错误: {e}")
                        raise Exception(f"Redis 异步其他错误: {e}") from e
        return self._async_redis

    # 同步操作方法
    def set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """设置键值对"""
        try:
            return self.redis.set(key, value, ex=ex)
        except redis.ConnectionError as e:
            print(f"Redis set 连接错误: {e}")
            return False
        except redis.TimeoutError as e:
            print(f"Redis set 超时错误: {e}")
            return False
        except redis.DataError as e:
            print(f"Redis set 数据错误: {e}")
            return False
        except redis.RedisError as e:
            print(f"Redis set 其他错误: {e}")
            return False

    def get(self, key: str) -> Optional[str]:
        """获取值"""
        try:
            return self.redis.get(key)
        except redis.ConnectionError as e:
            print(f"Redis get 连接错误: {e}")
            return None
        except redis.TimeoutError as e:
            print(f"Redis get 超时错误: {e}")
            return None
        except redis.DataError as e:
            print(f"Redis get 数据错误: {e}")
            return None
        except redis.RedisError as e:
            print(f"Redis get 其他错误: {e}")
            return None

    def delete(self, *keys: str) -> int:
        """删除键"""
        try:
            return self.redis.delete(*keys)
        except redis.ConnectionError as e:
            print(f"Redis delete 连接错误: {e}")
            return 0
        except redis.TimeoutError as e:
            print(f"Redis delete 超时错误: {e}")
            return 0
        except redis.RedisError as e:
            print(f"Redis delete 其他错误: {e}")
            return 0

    def expire(self, key: str, time: int) -> bool:
        """设置过期时间"""
        try:
            return self.redis.expire(key, time)
        except redis.ConnectionError as e:
            print(f"Redis expire 连接错误: {e}")
            return False
        except redis.TimeoutError as e:
            print(f"Redis expire 超时错误: {e}")
            return False
        except redis.DataError as e:
            print(f"Redis expire 数据错误: {e}")
            return False
        except redis.RedisError as e:
            print(f"Redis expire 其他错误: {e}")
            return False

    def ttl(self, key: str) -> int:
        """获取剩余过期时间"""
        try:
            return self.redis.ttl(key)
        except redis.ConnectionError as e:
            print(f"Redis ttl 连接错误: {e}")
            return -1
        except redis.TimeoutError as e:
            print(f"Redis ttl 超时错误: {e}")
            return -1
        except redis.RedisError as e:
            print(f"Redis ttl 其他错误: {e}")
            return -1

    # 异步操作方法

    async def async_set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """异步设置键值对"""
        try:
            redis_client = await self.get_async_redis()
            return await redis_client.set(key, value, ex=ex)
        except aioredis.ConnectionError as e:
            print(f"Redis async_set 连接错误: {e}")
            return False
        except aioredis.TimeoutError as e:
            print(f"Redis async_set 超时错误: {e}")
            return False
        except aioredis.DataError as e:
            print(f"Redis async_set 数据错误: {e}")
            return False
        except aioredis.RedisError as e:
            print(f"Redis async_set 其他错误: {e}")
            return False

    async def async_get(self, key: str) -> Optional[str]:
        """异步获取值"""
        try:
            redis_client = await self.get_async_redis()
            return await redis_client.get(key)
        except aioredis.ConnectionError as e:
            print(f"Redis async_get 连接错误: {e}")
            return None
        except aioredis.TimeoutError as e:
            print(f"Redis async_get 超时错误: {e}")
            return None
        except aioredis.DataError as e:
            print(f"Redis async_get 数据错误: {e}")
            return None
        except aioredis.RedisError as e:
            print(f"Redis async_get 其他错误: {e}")
            return None

    async def async_delete(self, *keys: str) -> int:
        """异步删除键"""
        try:
            redis_client = await self.get_async_redis()
            return await redis_client.delete(*keys)
        except aioredis.ConnectionError as e:
            print(f"Redis async_delete 连接错误: {e}")
            return 0
        except aioredis.TimeoutError as e:
            print(f"Redis async_delete 超时错误: {e}")
            return 0
        except aioredis.RedisError as e:
            print(f"Redis async_delete 其他错误: {e}")
            return 0

    async def async_exists(self, key: str) -> bool:
        """异步检查键是否存在"""
        try:
            redis_client = await self.get_async_redis()
            return bool(await redis_client.exists(key))
        except aioredis.ConnectionError as e:
            print(f"Redis async_exists 连接错误: {e}")
            return False
        except aioredis.TimeoutError as e:
            print(f"Redis async_exists 超时错误: {e}")
            return False
        except aioredis.RedisError as e:
            print(f"Redis async_exists 其他错误: {e}")
            return False

    async def async_expire(self, key: str, time: int) -> bool:
        """异步设置过期时间"""
        try:
            redis_client = await self.get_async_redis()
            return await redis_client.expire(key, time)
        except aioredis.ConnectionError as e:
            print(f"Redis async_expire 连接错误: {e}")
            return False
        except aioredis.TimeoutError as e:
            print(f"Redis async_expire 超时错误: {e}")
            return False
        except aioredis.DataError as e:
            print(f"Redis async_expire 数据错误: {e}")
            return False
        except aioredis.RedisError as e:
            print(f"Redis async_expire 其他错误: {e}")
            return False

    async def async_ttl(self, key: str) -> int:
        """异步获取剩余过期时间"""
        try:
            redis_client = await self.get_async_redis()
            return await redis_client.ttl(key)
        except aioredis.ConnectionError as e:
            print(f"Redis async_ttl 连接错误: {e}")
            return -1
        except aioredis.TimeoutError as e:
            print(f"Redis async_ttl 超时错误: {e}")
            return -1
        except aioredis.RedisError as e:
            print(f"Redis async_ttl 其他错误: {e}")
            return -1

    def close(self):
        """关闭连接"""
        if hasattr(self, 'redis'):
            self.redis.close()

    async def async_close(self):
        """异步关闭连接"""
        if self._async_redis:
            await self._async_redis.close()

    @classmethod
    def get_instance(cls) -> 'RedisFunction':
        """获取单例实例"""
        return cls()


# 全局实例
# REDIS_OBJ = RedisFunction.get_instance()

def get_redis():
    """获取Redis实例的依赖注入函数"""
    try:
        return RedisFunction.get_instance()
    except Exception as e:
        print(f"初始化redis 链接失败{e}")
        return None

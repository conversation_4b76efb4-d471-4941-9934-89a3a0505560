# -*- coding: utf-8 -*-
"""Start application"""
import asyncio
import uvicorn
from app.db import check_db_connection
from app.web_server import create_app
from setting import SERVER_PORT, DEBUG

app = create_app()


async def startup_check():
    """check db connect"""
    return await check_db_connection()

if __name__ == "__main__":
    result = asyncio.run(startup_check())
    if result:
        uvicorn.run("atlas_server:app", host="0.0.0.0",
                    port=SERVER_PORT, reload=DEBUG)

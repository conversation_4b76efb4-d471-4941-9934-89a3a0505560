# -*- coding: utf-8 -*-
"""python 函数"""
import re
import time
import hashlib
from datetime import datetime  # , timedelta, timezone
import random
import string


def get_datetime(time_type="1"):
    """时间戳转换为时间格式"""
    int_time = int(time.time())
    if "1" == time_type:
        return time.strftime(
            "%Y-%m-%d %H:%M:%S", time.localtime(int_time))
    return int_time


def get_diff_seconds(start_time: str, end_time=None) -> int:
    """获取时间差 单位秒"""
    if not start_time:
        return 0
    if not end_time:
        end_time = get_datetime()
    else:
        end_time = time.strptime(end_time, "%Y-%m-%d %H:%M:%S")
    start_time = time.strptime(start_time, "%Y-%m-%d %H:%M:%S")
    return int(time.mktime(end_time)) - int(time.mktime(start_time))


def get_today(split_str=""):
    """获取今天信息"""
    now = datetime.now()
    return now.strftime('%Y{0}%m{0}%d').format(split_str)


def verify_email(email):
    """verify email formate

    Args:
        email (_type_): _description_

    Returns:
        bool:  True or False
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not isinstance(email, str) or "" == email:
        return False
    # 使用re.match进行匹配
    if re.match(pattern, email):
        return True
    return False


def verify_phone(phone_number) -> bool:
    """verify phone formate

    Args:
        phone_number (_type_): _description_

    Returns:
        bool:  True or False
    """
    phone = str(phone_number)
    if not phone.isdigit():
        return False
    pattern = r"^1\d{10}$"

    if re.match(pattern, phone):
        return True
    return False


def verify_password_level(password):
    """
    verify password secrat level
    """
    pattern = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$'

    # 使用re.match进行匹配
    if re.match(pattern, password):
        return True

    return False


def sha1(input_string):
    """对输入的字符串进行 SHA-256 哈希加密"""
    hash_object = hashlib.sha256()
    hash_object.update(input_string.encode('utf-8'))
    hashed_string = hash_object.hexdigest()

    return hashed_string


def get_random_code(length=64):
    """生成指定长度的随机字符串"""
    # 定义字符集
    characters = string.ascii_letters + string.digits

    # 生成随机字符串
    random_string = ''.join(random.choice(characters) for _ in range(length))

    return random_string

# -*- coding: utf-8 -*-
"""python  亚马逊店铺信息 处理"""
import requests
from setting import AMAZON_SP_APPLICATION_ID, AMAZON_SP_RETURN_URL, ACCESS_TOKEN_DAILI_URL, \
    AMAZON_SP_SECRET, AMAZON_SP_PARTNER_ID, AMAZON_AD_PARTNER_ID, AMAZON_AD_RETURN_URL, AMAZON_AD_SECRET


DEFINE_AMAZON_URL = {
    "ad_token_url": {
        'NA': 'https://api.amazon.com/auth/o2/token',
        'EU': 'https://api.amazon.co.uk/auth/o2/token',
        'FE': 'https://api.amazon.co.jp/auth/o2/token',
    },
    "amazon_outh_uri": "/apps/authorize/consent?application_id={}&state={}&redirect_uri={}",  # sp
    "amazon_outh_uri_ad": "/ap/oa?client_id={}&scope=advertising::audiences&response_type=code&state={}&redirect_uri={}",
    "amazon_sp_return_url": "/api/amazon/sp_outh_callback",
    "amazon_ad_return_url": "/api/amazon/ad_outh_callback",
    "amazon_base_domain": {
        "SP": {
            "NA": 'https://sellercentral.amazon.com',
            "EU": "https://sellercentral-europe.amazon.com",
            "JP": "https://sellercentral.amazon.co.jp",
            "SG": "https://sellercentral.amazon.sg",
            "AU": "https://sellercentral.amazon.com.au",
            "SA": "https://sellercentral.amazon.sa",
            "AE": "https://sellercentral.amazon.ae"
        },
        "AD": {
            "NA": "https://sellercentral.amazon.com",
            "EU": "https://eu.account.amazon.com",
            "FE": "https://apac.account.amazon.com"
        }
    },
    "amazon_base_domain_vendor": {
        "SP": {
            "CA": "https://vendorcentral.amazon.ca",
            "US": "https://vendorcentral.amazon.com",
            "MX": "https://vendorcentral.amazon.com.mx",
            "BR": "https://vendorcentral.amazon.com.br",
            "IE": "https://vendorcentral.amazon.com.ie",
            "ES": "https://vendorcentral.amazon.es",
            "UK": "https://vendorcentral.amazon.co.uk",
            "FR": "https://vendorcentral.amazon.fr",
            "BE": "https://vendorcentral.amazon.be",
            "NL": "https://vendorcentral.amazon.nl",
            "DE": "https://vendorcentral.amazon.de",
            "IT": "https://vendorcentral.amazon.it",
            "SE": "https://vendorcentral.amazon.se",
            "ZA": "https://vendorcentral.amazon.co.za",
            "PL": "https://vendorcentral.amazon.pl",
            "EG": "https://vendorcentral.amazon.eg",
            "SA": "https://vendorcentral.amazon.sa",
            "TR": "https://vendorcentral.amazon.com.tr",
            "AE": "https://vendorcentral.amazon.ae",
            "IN": "https://vendorcentral.amazon.in",
            "NA": 'https://vendorcentral.amazon.com',
            "SG": "https://vendorcentral.amazon.com.sg",
            "AU": "https://vendorcentral.amazon.com.au",
            "JP": "https://vendorcentral.amazon.co.jp"
        },
        "AD": {
            "NA": "https://sellercentral.amazon.com",
            "EU": "https://eu.account.amazon.com",
            "FE": "https://apac.account.amazon.com"
        }
    }
}
# 亚马逊店铺类
PROXY = {
    "https" if ACCESS_TOKEN_DAILI_URL.startswith("https")
    else "http": ACCESS_TOKEN_DAILI_URL
} if ACCESS_TOKEN_DAILI_URL else None


class AmazonShop:
    """亚马逊店铺类"""
    timeout = 30
    headers = {
        'User-Agent': "python-ad-api",
        'content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }

    @classmethod
    def get_ad_auth_url(cls, **config):
        """获取 AD 授权 URL"""
        app_id: str = AMAZON_AD_PARTNER_ID  # 亚马逊应用ID
        # country_code: str = config.get('country_code')
        area_code: str = config.get('area_code', None)
        state: str = config.get('state', None)
        shop_is_vendor: bool = config.get('shop_is_vendor', False)
        if shop_is_vendor:
            base_domain = DEFINE_AMAZON_URL['amazon_base_domain_vendor']['AD'].get(
                area_code, False)
        else:
            base_domain = DEFINE_AMAZON_URL['amazon_base_domain']['AD'].get(
                area_code, False)
        if not base_domain:
            return False, "Areacode error, no base domain"

        jump_url = base_domain + DEFINE_AMAZON_URL['amazon_outh_uri_ad'].format(
            app_id, state, AMAZON_AD_RETURN_URL)
        return True, jump_url

    @classmethod
    def get_ad_api_authorization(cls, **config):
        """这里是验证ad 获取token 新版本通过api授权的
            返回：
            {
            "access_token":'xx',
                "token_type":'Bearer',
                "expires_in":3600,
                "refresh_token":'xx'
            }
            # todo
        """
        return {
            "access_token": 'xx',
            "token_type": 'Bearer',
            "expires_in": 3600,
            "refresh_token": 'xx'
        }
        request_url = DEFINE_AMAZON_URL['ad_token_url'].get(
            config.get('area'))
        post_data = {
            "grant_type": 'authorization_code',
            "code": config.get('code'),
            "client_id": AMAZON_AD_PARTNER_ID,
            "client_secret": AMAZON_AD_SECRET,
            "redirect_uri": AMAZON_AD_RETURN_URL,
        }
        return cls._request(request_url, post_data, cls.headers)

    @classmethod
    def get_sp_auth_url(cls, **config):
        """获取 SP 授权 URL"""
        app_id: str = AMAZON_SP_APPLICATION_ID  # 亚马逊应用ID
        # country_code: str = config.get('country_code')
        area_code: str = config.get('area_code', None)
        state: str = config.get('state', None)
        return_url: str = AMAZON_SP_RETURN_URL
        is_vender: bool = config.get('is_vender', False)
        country_code: str = config.get('country_code', '')
        if not app_id or not area_code or not state or not return_url:
            return False, "app_id or area_code or state or return_url is None"

        if not is_vender:
            # 如果是普通销售
            base_domain = DEFINE_AMAZON_URL['amazon_base_domain']['SP'].get(
                area_code, False)
        else:
            # 如果是 销售商
            base_domain = DEFINE_AMAZON_URL['amazon_base_domain_vendor']['SP'].get(
                country_code[0:2], False)  # 取前两位

        if not base_domain:
            return False, "Areacode or Countrycode error, no base domain"

        jump_uri = base_domain + DEFINE_AMAZON_URL['amazon_outh_uri'].format(
            app_id, state, return_url)

        return True, jump_uri

    @classmethod
    def get_sp_api_authorization(cls, spapi_oauth_code):
        """获取 SP 授权 access_token
        返回参数：
            {
            'access_token': token_info.get('access_token', ''),
            'token_type': token_info.get('token_type', ''),
            'expires_in': token_info.get('expires_in', 0),
            'refresh_token': token_info.get('refresh_token', '')
            }
        """
        # todo 测试
        return {
            'access_token': '123',
            'token_type': 'Bearer',
            'expires_in': 3600,
            'refresh_token': '123'
        }

        request_url = "https://api.amazon.com/auth/o2/token"
        post_data = {
            "grant_type": 'authorization_code',
            "code": spapi_oauth_code,  # 亚马逊返回的 spapi_oauth_code
            "client_id": AMAZON_SP_PARTNER_ID,
            "client_secret": AMAZON_SP_SECRET,
            "redirect_uri": AMAZON_SP_RETURN_URL
        }
        return cls._request(request_url, post_data, cls.headers)

    @classmethod
    def _request(cls, url, data, headers, try_num=0):
        """请求获取token"""
        try:
            response = requests.post(
                url,
                data=data,
                headers=headers,
                timeout=cls.timeout,
                proxies=PROXY if "co.jp" in url else None,
                verify=True,
            )
            response_data = response.json()
            if response.status_code != 200:
                error_message = response_data.get('error_description')
                error_code = response_data.get('error')
                print(error_message, error_code)
                try_num = try_num + 1
                if try_num > 2:
                    return False
                return cls._request(url, data, headers, try_num)
            return response_data
        except Exception as e:
            print(e)
            try_num = try_num + 1
            if try_num > 2:
                return False
            return cls._request(url, data, headers, try_num)

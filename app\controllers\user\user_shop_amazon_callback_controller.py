# -*- coding: utf-8 -*-
"""亚马逊 回调 信息处理，主要用于 授权回调。这里有大量权限状态判断"""

from app.controllers.user.base_user_controller import BaseUserController
from app.models.amazon_shop_model import UserVitualShopModel, AmazonShopSpInfoModel, \
    AmazonShopCountryModel, UserVirtualShopCountryRelationModel, AmazonShopAdInfo
from app.tps.amazon import AmazonShop
from app.tps.amazon_ad_auth_get_account import AmazonAdAuthGetAccount
from app.logger import Logger
from app.utils.function import get_datetime


class UserShopAmazonCallBackController(BaseUserController):
    """用户店铺亚马逊 回调操作"""
    logger = Logger.get_instance("amazon_shop_callback")

    async def amazon_ad_callback(self):
        """亚马逊广告回调"""
        post_data = await self.request.json()
        self.logger.info(f"amazon_ad_callback: {post_data}")
        adapi_oauth_code = post_data.get('code', '')
        state = post_data.get('state', '')
        if not state or not adapi_oauth_code:
            return self.r(code=104001, msg="Invalid state")
        # 判断state
        v_shop_info = await self._get_ad_auth_shop(state)
        if not v_shop_info:
            return self.r(code=104002, msg="Invalid state")
        # 获取验证 信息
        ad_token_info = AmazonShop.get_ad_api_authorization(
            area=v_shop_info['ad_auth_temp_area'],
            code=adapi_oauth_code,
        )
        # 如果验证失败
        if not ad_token_info:
            return self.r(code=104012, msg="Invalid oauth code")
        # 如果成功
        amazon_account_info = AmazonAdAuthGetAccount(
            v_shop_info['ad_auth_temp_area'], ad_token_info.get('access_token')).get_profiles()
        amazon_account_id = ""
        if isinstance(amazon_account_info, list) and len(amazon_account_info) > 0:
            # 获取国家对应的名字
            country_names = {}
            ad_auth_type = 'vendor' if v_shop_info['virtual_shop_vendor'] == 1 else 'seller'
            for item in amazon_account_info:
                if item.get('accountInfo').get('type') == ad_auth_type:
                    country_names[item.get('countryCode')
                                  ] = item['accountInfo'].get('name')
                    amazon_account_id = item.get('accountInfo').get('id')
        else:
            return self.r(code=104013, msg="Invalid access_token, please try again")
        # 判断 SP 和AD 的店铺一致性
        shop_sp_info = await AmazonShopSpInfoModel.get_one_conditions(
            conditions={'id': v_shop_info['temp_sp_id_for_ad_auth']}
        )
        print(amazon_account_info)
        if not shop_sp_info or shop_sp_info['selling_partner_id'] != amazon_account_id:
            return self.r(code=104014, msg="Amazon SP and AD authorization are not the same store, please try again")
        # 如果sp 绑定店铺 ad id 为 0 则表示新绑定 -- 这里是事务处理
        shop_ad_info = {
            "ad_selling_partner_id": amazon_account_id,
            "ad_mws_auth_token": "",
            "ad_api_oauth_code": adapi_oauth_code,
            "ad_access_token": ad_token_info.get('access_token'),
            "ad_token_type": ad_token_info.get('token_type'),
            "ad_expires_in": ad_token_info.get('expires_in'),
            "ad_refresh_token": ad_token_info.get('refresh_token'),
            "ad_expires_timestamp": get_datetime('timestamp') + ad_token_info.get('expires_in'),
            "ad_area_code": v_shop_info['ad_auth_temp_area'],
            "ad_last_auth_datetime": get_datetime(),
            "ad_last_auth_user_id": v_shop_info['user_id'],
        }

        if shop_sp_info['shop_ad_info_id'] == 0:
            print("添加")
            # 新增记录 -- 更新当前信息
            shop_ad_info['ad_first_user_id'] = v_shop_info['user_id']
            shop_ad_info['ad_first_auth_datetime'] = get_datetime()
            try:
                if self.db.in_transaction():
                    await self.db.rollback()
                async with self.db.begin():
                    new_ad_info_id = await AmazonShopAdInfo.add_one(
                        data=shop_ad_info,
                        is_transaction=True
                    )
                    if not new_ad_info_id:
                        raise ValueError("Add ad info error")
                    update_sp_result = await AmazonShopSpInfoModel.update_conditions(
                        conditions={
                            "id": v_shop_info['temp_sp_id_for_ad_auth']},
                        new_values={"shop_ad_info_id": new_ad_info_id},
                        is_transaction=True
                    )
                    if not update_sp_result:
                        raise ValueError("Update sp info error")
                    # 提交
                    await self.db.commit()
                ad_result = True
            except Exception as e:
                print(e)
                ad_result = False
                # 关闭 事务
                await self.db.close()
        # 否则 表示 更新
        else:
            # 关闭事务
            print("更新")
            # 更新国家店铺 名称
            ad_result = await AmazonShopAdInfo.update_conditions(
                conditions={
                    "id": shop_sp_info['shop_ad_info_id']},
                new_values=shop_ad_info
            )
        if not ad_result:
            return self.r(code=104015, msg="Update ad info error")
        # 更新当前 用户授权信息, 获取关联店铺
        countrys = await AmazonShopCountryModel.get_conditions(
            conditions={
                "shop_sp_info_id": v_shop_info['temp_sp_id_for_ad_auth']
            }
        )
        country_ids = []
        # 更新国家名称
        for item in countrys:
            country_ids.append(item.get('id'))
            if item.get('country_code') in country_names:
                await AmazonShopCountryModel.update_conditions(
                    conditions={
                        "id": item.get('id')
                    },
                    new_values={
                        "ad_shop_name": country_names[item.get('country_code')]
                    }
                )
        # 更新 授权关系信息
        if country_ids:
            result = await UserVirtualShopCountryRelationModel.update_conditions(
                conditions={
                    "user_vitrual_shop_id": v_shop_info['id'],
                    "shop_country_id": ["in", country_ids]
                },
                new_values={
                    "shop_country_ad_datetime": get_datetime(),
                    "user_shop_country_ad_flag": 1
                }
            )
            print(result)
        return self.r()
        # 返回结果

    async def amazon_sp_callback(self):
        """获取亚马逊回调信息"""
        post_data = await self.request.json()
        self.logger.info(f"amazon_sp_callback: {post_data}")
        # 1. 校验参数
        # 2. 校验回调状态
        # 3. 校验店铺是否存在
        # 4. 更新店铺信息
        # 5. 返回结果
        sp_auth_temp_state = post_data.get("state")
        selling_partner_id = post_data.get('selling_partner_id')
        mws_auth_token = post_data.get('mws_auth_token')
        spapi_oauth_code = post_data.get('spapi_oauth_code')
        if not sp_auth_temp_state or not selling_partner_id or\
                not mws_auth_token or not spapi_oauth_code:
            return self.r(code=104007, msg="Invalid state")
        v_shop_info = await self._get_sp_auth_shop(sp_auth_temp_state)
        if not v_shop_info:
            return self.r(code=104008, msg="Invalid sp_auth_temp_state")
        # 验证SP 获取accesstoken
        token_info = AmazonShop.get_sp_api_authorization(spapi_oauth_code)
        if not token_info:
            return self.r(code=104009, msg="Invalid spapi_oauth_code")

        # 7. 返回结果
        shop_sp_info, is_new_sp_authed_id = await self._process_sp_shop_info(
            selling_partner_id, spapi_oauth_code, v_shop_info, token_info,
            mws_auth_token)
        if not shop_sp_info:
            return self.r(code=104010, msg="Invalid shop_sp_info")

        shop_country_info = await self._process_sp_shop_country_info(
            shop_sp_info,
            v_shop_info,
            is_new_sp_authed_id
        )
        # 处理 国家 和用户国家的关系
        result = await self._process_virtual_shop_country_relation(
            v_shop_info,
            shop_country_info
        )
        if result:
            return self.r(code=200, msg="Success")
        return self.r(code=104011, msg="Invalid shop_country_info")

    async def _process_virtual_shop_country_relation(self,  v_shop_info, shop_country_info):
        """ 处理 店铺与当前用户 关系 """

        # 查询所有当前用户关联关系
        user_shop_sp_relation = await UserVirtualShopCountryRelationModel.get_conditions(
            conditions={
                "user_vitrual_shop_id": v_shop_info['id'],
                "user_id": v_shop_info['user_id'],
            }
        )
        have_shop_ids = [one_relation['shop_country_id']
                         for one_relation in user_shop_sp_relation]
        need_add_relation = []
        for shop_country in shop_country_info:
            if shop_country['id'] not in have_shop_ids:
                need_add_relation.append(
                    {
                        "user_vitrual_shop_id": v_shop_info['id'],
                        "user_id": v_shop_info['user_id'],
                        "shop_country_id": shop_country['id'],
                        "shop_country_relation_addtime": get_datetime(),
                        "shop_country_role": '0',
                        "role_user_status": '0',
                        "user_apply_status": '0',
                        "shop_country_sp_datetime": get_datetime(),
                        "user_shop_country_sp_flag": 1
                    }
                )
        # 添加新的授权关系
        if need_add_relation:
            result = await UserVirtualShopCountryRelationModel.add_multy(
                data=need_add_relation
            )
            return result
        # 如果已经存在关系，则不处理
        return True

    async def _process_sp_shop_info(self,
                                    selling_partner_id,
                                    spapi_oauth_code,
                                    v_shop_info,
                                    token_info,
                                    mws_auth_token):
        """这里处理 店铺逻辑
            1 判断店铺是否存在，不存在添加，存在则 更新
            2 判断国家是否 存在，不存在则添加，存在则更新
            3 更新 当前用户 国家 和 虚拟店铺 关系
        """
        virtual_shop_vendor = v_shop_info.get('virtual_shop_vendor')
        sp_auth_temp_area = v_shop_info.get('sp_auth_temp_area')

        # 判断店铺是否存在，如果不存在则添加
        shop_info = await AmazonShopSpInfoModel.get_one_conditions(
            conditions={
                'sp_auth_temp_area': sp_auth_temp_area,
                'selling_partner_id': selling_partner_id,
            }
        )
        if shop_info:
            if shop_info['shop_is_vendor'] != virtual_shop_vendor:
                return False
            is_new_sp_authed_id = 0
            # 如果存在 则 更新 授权时间 和信息
            result = await AmazonShopSpInfoModel.update_conditions(
                conditions={
                    'id': shop_info['id'],
                },
                new_values={
                    "last_auth_datetime": get_datetime(),
                    "last_auth_user_id": v_shop_info['user_id'],
                }
            )
            if not result:
                return False

        else:
            shop_info = {
                "selling_partner_id": selling_partner_id,
                "mws_auth_token": mws_auth_token,
                "api_oauth_code": spapi_oauth_code,
                "access_token": token_info['access_token'],
                "token_type": token_info['token_type'],
                "expires_in": token_info['expires_in'],
                "refresh_token": token_info['refresh_token'],
                "expires_timestamp": get_datetime('timestamp') + token_info['expires_in'],
                "area_code": v_shop_info['sp_auth_temp_area'],
                "first_auth_datetime": get_datetime(),
                "first_user_id": v_shop_info['user_id'],
                "last_auth_datetime": get_datetime(),
                "last_auth_user_id": v_shop_info['user_id'],
                "shop_ad_info_id": 0,
                "shop_is_vendor": virtual_shop_vendor
            }
            is_new_sp_authed_id = await AmazonShopSpInfoModel.add_one(
                data=shop_info
            )
            if not is_new_sp_authed_id:
                return False
            shop_info['id'] = is_new_sp_authed_id

        return shop_info, is_new_sp_authed_id

    async def _process_sp_shop_country_info(self, shop_sp_info, v_shop_info, is_new_sp_authed_id):
        """ 处理店铺国家信息 """
        need_add_country = []
        now_datetime = get_datetime()
        country_array = v_shop_info['sp_auth_temp_country'].split(',')
        # have_country_ids = []

        if is_new_sp_authed_id > 0:
            # 如果是新的店铺，直接添加国家信息店铺
            new_country_array = country_array
        else:
            # 查询已经存在的国家
            have_country = await AmazonShopCountryModel.get_conditions(
                conditions={
                    "shop_sp_info_id": shop_sp_info['id'],
                }
            )
            new_country_array = []
            if have_country:
                for one_have_country in have_country:
                    if one_have_country['country_code'] not in country_array:
                        new_country_array.append(
                            one_have_country['country_code'])
            else:
                # 如果没有存在的国家，直接添加
                new_country_array = country_array
        for country in new_country_array:
            need_add_country.append(
                {
                    "shop_sp_info_id": shop_sp_info['id'],
                    "country_code": country,
                    "first_country_datetime": now_datetime,
                    "first_country_user_id": v_shop_info['user_id'],
                    "pay_user_id": 0,
                    "pay_account_id": 0,
                }
            )
        if need_add_country:
            await AmazonShopCountryModel.add_multy(
                data=need_add_country
            )
        # 查询所有的 -本次授权 的所有 关系
        have_country = await AmazonShopCountryModel.get_conditions(
            conditions={
                "shop_sp_info_id": shop_sp_info['id'],
                "country_code": ['in', country_array]
            }
        )
        return have_country

    async def _get_sp_auth_shop(self, sp_auth_temp_state: str):
        """根据 sp_auth_temp_state 获取店铺信息"""
        state_array = sp_auth_temp_state.split("_")
        if len(state_array) != 3:
            return False
        # 获取关联店铺信息
        user_id = self.request.state.login_user_id
        state = state_array[1]
        expire_timestamp = state_array[2]

        if int(expire_timestamp) < get_datetime('timestamp'):
            return False

        v_shop_info = await UserVitualShopModel.get_one_conditions(
            conditions={
                "user_id": user_id,
                "sp_auth_temp_state": state
            }
        )
        if not v_shop_info:
            return False
        if int(v_shop_info['sp_auth_temp_state_expire_timestamp']) != int(expire_timestamp):
            return False
        #  验证成功 则清除 当前用户店铺 sp state 属性
        await UserVitualShopModel.update_conditions(
            conditions={
                "id": v_shop_info['id'],
            },
            new_values={
                "sp_auth_temp_state": "",
                "sp_auth_temp_state_expire_timestamp": 0,
            }
        )
        return v_shop_info

    async def _get_ad_auth_shop(self, ad_auth_temp_state: str):
        """根据 sp_auth_temp_state 获取店铺信息"""
        state_array = ad_auth_temp_state.split("_")
        if len(state_array) != 4:
            return False
        # 获取关联店铺信息
        user_id = self.request.state.login_user_id
        state_user_id = state_array[0]
        state = state_array[1]
        expire_timestamp = state_array[2]
        temp_sp_id_for_ad_auth = state_array[3]

        if int(expire_timestamp) < get_datetime('timestamp') or int(state_user_id) != int(user_id):
            print("AD 授权过期或者用户不一致")
            return False

        v_shop_info = await UserVitualShopModel.get_one_conditions(
            conditions={
                "user_id": user_id,
                "ad_auth_temp_state": state
            }
        )
        if not v_shop_info:
            print("no shop")
            return False
        if int(v_shop_info['ad_auth_temp_state_expire_timestamp']) != int(expire_timestamp)\
                or int(v_shop_info['temp_sp_id_for_ad_auth']) != int(temp_sp_id_for_ad_auth):
            print("过期")
            return False
        #  验证成功 则清除 当前用户店铺 sp state 属性
        # await UserVitualShopModel.update_conditions(
        #     conditions={
        #         "id": v_shop_info['id'],
        #     },
        #     new_values={
        #         "ad_auth_temp_state": "",
        #         "ad_auth_temp_state_expire_timestamp": 0,
        #         "temp_sp_id_for_ad_auth": 0,
        #     }
        # )
        return v_shop_info

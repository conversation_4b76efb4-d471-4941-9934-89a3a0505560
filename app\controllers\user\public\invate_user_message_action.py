# -*- coding: utf-8 -*-
"""python 邀请用户的消息"""
from app.controllers.user.public.message_action import MessageAction
from app.utils.const import USER_SHOP_COUNTRY_ROLE


class InvateUserMessageAction(MessageAction):
    """邀请用户的消息"""

    @classmethod
    async def accept_invate_user_message(cls, new_user_config, other_user_config, from_invite_user):
        """接受邀请用户的消息"""
        # 新用户消息
        new_user_message = {
            "user_id": new_user_config.get("user_id"),
            "area_shop_id": new_user_config.get("shop_country_id"),
            "message_type": "ACCEPT_I",  # 接受邀请
            "message_title": "You have successfully joined",
            "message_content": (
                f"You have successfully joined 【{new_user_config.get('shop_name')}】"
                f"({new_user_config.get('country_code')}) as a "
                f"{USER_SHOP_COUNTRY_ROLE.get(new_user_config.get('country_role'))} member."
                "Welcome to the team."
            ),
        }
        # 添加信息
        await cls.add_user_message(**new_user_message)
        # 其他账号消息
        if other_user_config:
            base_message_config = {
                "area_shop_id": new_user_config.get("shop_country_id"),
                "message_type": "ACCEPT_I",  # 接受邀请
                "message_title": "New member joined",
            }
            for old_user in other_user_config:
                # 其他账号消息
                other_user_message = {
                    "user_id": old_user.get("user_id"),
                    "message_content": f"{new_user_config.get('email')} has joined"
                    f"【{old_user.get('shop_name')}】 ({old_user.get('country_code')})"
                    f" as a {USER_SHOP_COUNTRY_ROLE.get(new_user_config.get('shop_country_role'))}"
                    f" member. Invited person: {new_user_config.get('email')}  "
                    f"Invited by:{from_invite_user.get('nick_name')} "
                    f"{from_invite_user.get('email')}"
                    ** base_message_config,
                }
                # 添加信息
                await cls.add_user_message(**other_user_message)

    @classmethod
    async def add_invate_user_message(cls, admin_config, collaborator_config):
        """添加邀请用户的消息"""
        if collaborator_config:
            # 如果是协作邀请, 发送 邀请人 消息
            collaborator_config = {
                "user_id": collaborator_config.get("user_id"),
                "area_shop_id": collaborator_config.get("shop_country_id"),
                "message_type": "INVATE_U",  # 邀请用户
                "message_title": "New member invitation has been submitted",
                "message_content": (
                    f"You have invited {collaborator_config.get('email')} to join"
                    f" 【{collaborator_config.get('shop_name')}】"
                    f"({collaborator_config.get('country_code')}) as a "
                    f"{USER_SHOP_COUNTRY_ROLE.get(collaborator_config.get('country_role'))} member."
                ),
            }
            # 添加信息
            await cls.add_user_message(**collaborator_config)
            # 发送给管理者
            admin_message = {
                "user_id": admin_config.get("user_id"),
                "area_shop_id": admin_config.get("shop_country_id"),
                "message_type": "INVATE_U",  # 邀请用户
                "message_title": "New member invitation has been submitted",
                "message_content": (
                    f"{admin_config.get('email')} has been invited to join"
                    f" 【{admin_config.get('shop_name')}】 ({admin_config.get('country_code')})"
                    f" as a {USER_SHOP_COUNTRY_ROLE.get(admin_config.get('country_role'))} member."
                    f"Invited person:  {collaborator_config.get('email')}"
                    f"Invited by: {admin_config.get('from_email')}"
                ),
            }
            await cls.add_user_message(**admin_message)
        else:
            # 如果是管理员邀请
            admin_message = {
                "user_id": admin_config.get("user_id"),
                "area_shop_id": admin_config.get("shop_country_id"),
                "message_type": "INVATE_U",  # 邀请用户
                "message_title": "New member invitation has been submitted",
                "message_content": (
                    f"You have invited {admin_config.get('email')} to join"
                    f"【{admin_config.get('shop_name')}】 ({admin_config.get('country_code')})"
                    f" as a {USER_SHOP_COUNTRY_ROLE.get(admin_config.get('country_role'))} member."
                ),
            }
            # 添加信息
            await cls.add_user_message(**admin_message)

# -*- coding: utf-8 -*-
""" 测试模型 """
from sqlalchemy import Column, Date, Integer, String
from app.base_model import BaseModel


class TestModel(BaseModel):
    """测试模型"""
    __tablename__ = "test"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    create_time = Column(Date, nullable=False, default=0)


class Test2Model(BaseModel):
    """测试模型"""
    __tablename__ = "test2"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    create_time = Column(Date, nullable=False, default=0)

# -*- coding: utf-8 -*-
"""亚马逊店铺model"""

from operator import and_
from sqlalchemy.orm import aliased
from sqlalchemy import Column, Integer, String, DateTime, select
from app.base_model import BaseModel
from setting import TABLE_PREFIX


class UserVitualShopModel(BaseModel):
    """定义虚拟店铺"""
    __tablename__ = f'{TABLE_PREFIX}user_virtual_shop'

    id = Column(Integer, primary_key=True,
                autoincrement=True, comment='虚拟店铺iD')
    user_id = Column(Integer, nullable=False, comment='所属用户ID')
    shop_name = Column(String(128), nullable=False, comment='店铺名称')
    add_datetime = Column(DateTime, nullable=True, comment='添加时间')
    sp_auth_temp_state = Column(
        String(128), nullable=True, comment='sp 授权临时code')
    ad_auth_temp_state = Column(
        String(128), nullable=True, comment='ad 临时授权state')
    sp_auth_temp_area = Column(String(2), nullable=True, comment='sp 授权临时地区')
    ad_auth_temp_area = Column(String(128), nullable=True, comment='ad 授权临时地区')
    sp_auth_temp_country = Column(
        String(128), nullable=True, comment='SP临时授权国家代码 以英文逗号分隔')
    sp_auth_temp_state_expire_timestamp = Column(
        Integer, nullable=False, default=0, comment='SP 授权code 过期时间 ，时间戳')
    ad_auth_temp_state_expire_timestamp = Column(
        Integer, nullable=False, default=0, comment='ad 授权超时时间戳')
    virtual_shop_delete_flag = Column(
        Integer, nullable=False, default=0, comment='虚拟店铺是否删除默认0 否')

    virtual_shop_vendor = Column(
        Integer, nullable=False, default=0, comment='店铺是否是 供应商 0 不是 1 是')
    temp_sp_id_for_ad_auth = Column(
        Integer, nullable=False, default=0, comment='临时sp id 用于ad 授权')


class AmazonShopSpInfoModel(BaseModel):
    """定义店铺sp信息"""
    __tablename__ = 'amazon_shop_sp_info'
    id = Column(Integer, primary_key=True,
                autoincrement=True, comment='店铺 sp 授权序号')
    selling_partner_id = Column(
        String(64), nullable=False, comment='亚马逊销售额做伙伴id')
    mws_auth_token = Column(String(
        128), nullable=False, comment='mws_auth_token 亚马逊回来了，Seller applications only')
    api_oauth_code = Column(String(64), nullable=False,
                            comment='SP api_oauth_code')
    access_token = Column(String(128), nullable=False,
                          comment='SP access_token')
    token_type = Column(String(16), nullable=False, comment='token_type')
    expires_in = Column(Integer, nullable=False, comment='有效期')
    refresh_token = Column(String(128), nullable=False,
                           comment='sp refresh_token')
    expires_timestamp = Column(
        Integer, nullable=False, comment='access token 过期时间戳')
    area_code = Column(String(2), nullable=False, comment='所属地区')
    first_auth_datetime = Column(DateTime, nullable=False, comment='添加时间')
    first_user_id = Column(Integer, nullable=False, comment='默认 首次授权 用户')
    last_auth_datetime = Column(DateTime, nullable=False, comment='最后授权更新时间')
    last_auth_user_id = Column(Integer, nullable=False, comment='最后更新 用户ID')
    shop_ad_info_id = Column(Integer, nullable=False,
                             default=0, comment='店铺授权 AD 信息ID')
    shop_is_vendor = Column(Integer, nullable=False,
                            default=0, comment='店铺是否是 供应商 0 不是 1 是')
    local_db_name = Column(String(256), nullable=False, comment='店铺数据库名称')
    local_log_db_name = Column(
        String(256), nullable=False, comment='店铺日志数据库名称')
    is_close = Column(Integer, nullable=False, default=0,
                      comment='店铺地区是否关闭 0 不是 1 是')

    @classmethod
    async def get_sp_ad_info(cls, conditions):
        """获取店铺sp ad 信息"""
        query = select(
            cls,
            AmazonShopAdInfo.ad_area_code
        ).join(
            AmazonShopAdInfo, cls.shop_ad_info_id == AmazonShopAdInfo.id)
        if conditions:
            filters = cls.make_filters(conditions=conditions)
            # 进行条件查询
            if len(filters) == 1:
                # 如果只有一个过滤条件，直接使用它
                query = query.filter(filters[0])
            else:
                # 有多个过滤条件时使用 and_
                query = query.filter(and_(*filters))
        # 获取查询结果
        data_list = await cls.db().execute(query)
        data_list = data_list.fetchall()
        dict_list = []
        for row in data_list:
            model_obj = row[0]  # 第一个元素是模型对象
            ad_area_code = row[1]  # 第二个元素是 ad_area_code
            # 处理所有可能的序列化问题
            model_dict = cls.to_dict(model_obj)
            model_dict['ad_area_code'] = ad_area_code
            dict_list.append(model_dict)

        return dict_list


class AmazonShopAdInfo(BaseModel):
    """定义店铺ad 信息"""
    __tablename__ = 'amazon_shop_ad_info'
    id = Column(Integer, primary_key=True,
                autoincrement=True, comment='店铺 ad 授权序号')
    ad_selling_partner_id = Column(
        String(64), nullable=False, comment='ad 亚马逊销售额做伙伴id')
    ad_mws_auth_token = Column(String(
        128), nullable=False, comment='ad mws_auth_token')
    ad_api_oauth_code = Column(String(64), nullable=False,
                               comment='ad api_oauth_code')
    ad_access_token = Column(String(128), nullable=False,
                             comment='ad access_token')
    ad_token_type = Column(String(16), nullable=False, comment='ad token_type')
    ad_expires_in = Column(Integer, nullable=False, comment='ad 有效期')
    ad_refresh_token = Column(String(128), nullable=False,
                              comment='ad refresh_token')
    ad_expires_timestamp = Column(Integer, nullable=False,
                                  comment='ad access token 过期时间戳')
    ad_area_code = Column(String(2), nullable=False, comment='ad 所属地区')
    ad_first_auth_datetime = Column(
        DateTime, nullable=False, comment='ad 添加时间')
    ad_first_user_id = Column(Integer, nullable=False, comment='默认 首次授权 用户')
    ad_last_auth_datetime = Column(
        DateTime, nullable=False, comment='最后授权更新时间')
    ad_last_auth_user_id = Column(Integer, nullable=False, comment='最后更新 用户ID')


class AmazonShopCountryModel(BaseModel):
    """授权国家"""
    __tablename__ = 'amazon_shop_country'
    id = Column(Integer, primary_key=True,
                autoincrement=True, comment='国家ID')
    shop_sp_info_id = Column(Integer, nullable=False, comment='授权 SP ID')
    country_code = Column(String(2), nullable=False, comment='国家代码')
    first_country_datetime = Column(
        DateTime, nullable=False, comment='首次授权时间')
    first_country_user_id = Column(Integer, nullable=False, comment='首次授权用户ID')
    pay_user_id = Column(Integer, nullable=False, default=0, comment='支付用户ID')
    pay_account_id = Column(Integer, nullable=False,
                            default=0, comment='支付账号ID')
    first_pay_datetime = Column(
        DateTime, nullable=False, comment='首次 支付时间')
    ad_service_active = Column(Integer, nullable=False,
                               default=0, comment='ad 服务是否激活')
    activate_end_datetime = Column(
        DateTime, nullable=False, comment='激活结束时间')
    get_amazon_data = Column(Integer, nullable=False,
                             default=0, comment='是否获取了亚马逊 店铺 数据')
    get_amazon_data_datetime = Column(
        DateTime, nullable=False, comment='获取亚马逊店铺时间')
    parent_asin_num = Column(Integer, nullable=False,
                             default=0, comment='拥有父级 Asin 数量')
    parent_asin_authed = Column(
        Integer, nullable=False, default=0, comment='父级授权个数')
    parent_asin_auth_limit = Column(
        Integer, nullable=False, default=50, comment='限制Asin 投放个数')
    country_report_not_englist = Column(
        Integer, nullable=False, default=0, comment='非英语')
    ad_shop_name = Column(String(255), nullable=False, comment='amazon店铺名称')


class UserVirtualShopCountryRelationModel(BaseModel):
    """用户虚拟店铺国家用户关系"""
    __tablename__ = f'{TABLE_PREFIX}user_virtual_shop_country_relation'
    id = Column(Integer, primary_key=True,
                autoincrement=True, comment='关系ID')
    user_vitrual_shop_id = Column(
        Integer, nullable=False, comment='关联虚拟店铺ID-仅用于展示店铺名称')
    user_id = Column(Integer, nullable=False, comment='非空所属用户/或者是 申请用户')
    shop_country_relation_addtime = Column(
        DateTime, nullable=False, comment='添加 时间, 申请时间')
    shop_country_role = Column(
        String(1), nullable=False, default="0", comment='店铺国家权限 0 无权限 1 只读者 2 协同 3 管理员')
    role_user_status = Column(String(1), nullable=False, default="0",
                              comment='成员状态 0无关系 1 已经加入 2 申请中 3 已经移除')
    user_apply_status = Column(String(1), nullable=False, default="0",
                               comment='用户申请状态 0 无关系 1 待审批 2 已经拒绝 3 已经同意 4 已经超时 5 已经撤销')
    user_apply_datetime = Column(
        DateTime, nullable=True, comment='用户申请时间')
    user_apply_info = Column(String(255), nullable=True, comment='用户申请信息')
    agree_datetime = Column(
        DateTime, nullable=True, comment='同意时间')
    shop_country_id = Column(Integer, nullable=False, comment='授权国家ID')
    shop_country_sp_datetime = Column(
        DateTime, nullable=False, comment='店铺 国家 sp 授权时间')
    shop_country_ad_datetime = Column(
        DateTime, nullable=False, comment='Ad 授权时间')
    user_shop_country_sp_flag = Column(
        Integer, nullable=False, comment='用户是否已经 SP授权')
    user_shop_country_ad_flag = Column(
        Integer, nullable=False, comment='是否已经AD授权')

    @classmethod
    async def get_user_country_v_shop_one(cls, conditions):
        """获取条件 单个 用户与国家店铺关系, 只获取 1个"""
        query = select(
            cls,
            UserVitualShopModel.shop_name,
            AmazonShopCountryModel.country_code
        ).join(
            UserVitualShopModel, cls.user_vitrual_shop_id == UserVitualShopModel.id
        ).join(
            AmazonShopCountryModel, cls.shop_country_id == AmazonShopCountryModel.id
        )
        if conditions:
            filters = cls.make_filters(conditions)
            if len(filters) > 1:
                query = query.filter(*filters)
            else:
                query = query.filter(filters[0])
        # 获取所有数据
        query = query.order_by(cls.id.desc())
        result = await cls.db().execute(query)
        data = result.fetchone()
        if data:
            relation = cls.to_dict(data[0])
            shop_name = data[1]
            country_code = data[2]
            return {
                **relation,
                "shop_name": shop_name,
                "country_code": country_code}
        return None

    @classmethod
    async def get_user_country_v_shop_list(cls, conditions):
        """获取条件 单个 用户与国家店铺关系"""
        query = select(cls, UserVitualShopModel.shop_name).join(
            UserVitualShopModel, cls.user_vitrual_shop_id == UserVitualShopModel.id
        )
        if conditions:
            filters = cls.make_filters(conditions)
            if len(filters) > 1:
                query = query.filter(*filters)
            else:
                query = query.filter(filters[0])
        # 获取所有数据
        query = query.order_by(cls.id.desc())
        result = await cls.db().execute(query)
        data_list = []
        for item in result:
            relation = cls.to_dict(item[0])
            shop_name = item[1]
            data_list.append({**relation, "shop_name": shop_name})
        return data_list

    @classmethod
    async def get_user_relation(cls, conditions, user_model):
        """获取用户与关系信息 联合用户 user_model 进行查询"""
        query = select(cls, user_model.email).join(
            user_model, cls.user_id == user_model.id
        )
        if conditions:
            filters = cls.make_filters(conditions)
            if len(filters) > 1:
                query = query.filter(*filters)
            else:
                query = query.filter(filters[0])
        # 获取所有数据
        query = query.order_by(cls.role_user_status.desc())
        # order
        data_list = []
        result = await cls.db().execute(query)
        for item in result:
            relation = cls.to_dict(item[0])
            user_email = item[1]
            data_list.append({**relation, "email": user_email})

        return data_list

    @classmethod
    async def get_user_shop_list(cls, user_id: int):
        """获取用户店铺列表 以关系表为主"""

        # 先筛选relation表作为临时主表，提高查询效率
        # 首先筛选当前用户的有效关系记录
        relation_subquery = select(
            cls.id,
            cls.user_vitrual_shop_id,
            cls.shop_country_id,
            cls.shop_country_role,
            cls.role_user_status,
            cls.user_apply_status,
            cls.user_apply_datetime,
            cls.shop_country_sp_datetime,
            cls.shop_country_ad_datetime,
            cls.user_shop_country_sp_flag,
            cls.user_shop_country_ad_flag,
            cls.shop_country_relation_addtime
        ).where(
            cls.user_id == user_id
        ).subquery()
        # cls.role_user_status == 1  # 已经加入的成员

        # 创建表别名
        virtual_shop_alias = aliased(UserVitualShopModel)
        country_alias = aliased(AmazonShopCountryModel)
        sp_info_alias = aliased(AmazonShopSpInfoModel)
        ad_info_alias = aliased(AmazonShopAdInfo)

        # 基于筛选后的relation子查询进行联表查询
        query = select(
            relation_subquery.c.id.label('relation_id'),
            relation_subquery.c.shop_country_role,
            relation_subquery.c.role_user_status,
            relation_subquery.c.user_apply_status,
            relation_subquery.c.shop_country_sp_datetime,
            relation_subquery.c.shop_country_ad_datetime,
            relation_subquery.c.user_shop_country_sp_flag,
            relation_subquery.c.user_shop_country_ad_flag,
            virtual_shop_alias.id.label('virtual_shop_id'),
            virtual_shop_alias.shop_name,
            virtual_shop_alias.add_datetime.label('shop_add_datetime'),
            virtual_shop_alias.virtual_shop_vendor,
            country_alias.id.label('country_id'),
            country_alias.country_code,
            country_alias.activate_end_datetime,
            country_alias.get_amazon_data,
            country_alias.parent_asin_num,
            country_alias.parent_asin_authed,
            country_alias.parent_asin_auth_limit,
            country_alias.ad_shop_name,
            country_alias.ad_service_active,
            country_alias.country_report_not_englist,
            sp_info_alias.id.label('sp_info_id'),
            sp_info_alias.selling_partner_id,
            sp_info_alias.area_code.label('sp_area_code'),
            ad_info_alias.id.label('ad_info_id'),
            ad_info_alias.ad_selling_partner_id,
            ad_info_alias.ad_area_code
        ).select_from(
            relation_subquery
            .join(virtual_shop_alias,
                  relation_subquery.c.user_vitrual_shop_id == virtual_shop_alias.id)
            .join(country_alias,
                  relation_subquery.c.shop_country_id == country_alias.id)
            .join(sp_info_alias,
                  country_alias.shop_sp_info_id == sp_info_alias.id)
            .outerjoin(ad_info_alias,
                       sp_info_alias.shop_ad_info_id == ad_info_alias.id)
        ).where(
            virtual_shop_alias.virtual_shop_delete_flag == 0  # 未删除的虚拟店铺
        ).order_by(
            relation_subquery.c.user_vitrual_shop_id.desc(),  # 按照店铺倒序排序
            sp_info_alias.id.asc()
        )

        db = cls.db()
        try:
            result = await db.execute(query)
            data_list = result.fetchall()
            # 转换出dict 列出所有字段
            dict_list = []
            for row in data_list:
                row_dict = dict(row._mapping)
                # 处理所有可能的序列化问题
                for key, value in row_dict.items():
                    row_dict[key] = cls.serialize_for_json(value)
                dict_list.append(row_dict)
            return dict_list
        except Exception as e:
            print(f"查询用户店铺列表错误: {e}")
            return []

# -*- coding: utf-8 -*-
"""python 文件描述"""
from app.models.amazon_shop_model import UserVirtualShopCountryRelationModel, \
    AmazonShopCountryModel, UserVitualShopModel

from app.models.user_model import UserModel, UserInvateByEmailModel
from app.controllers.user.base_user_controller import BaseUserController
from app.utils.function import get_datetime, get_random_code, sha1, get_diff_seconds
from app.utils.email_function import EmailFunction
from app.controllers.user.public.shop_right_message_action import ShopRightMessageAction
from app.controllers.user.public.invate_user_message_action import InvateUserMessageAction
from app.utils.const import USER_SHOP_COUNTRY_ROLE, USER_ROLE_USER_STATUS, USER_APPLY_STATUS


class UserShopRightController(BaseUserController):
    """用户店铺权限操作类"""
    op_type = {
        "agree": "_process_shop_right_agree",
        "reject": "_process_shop_right_reject",
        "change_role": "_process_shop_right_change_role",
        "switch_admin": "_process_shop_right_switch_admin",
        "remove": "_process_shop_right_remove",
    }

    async def process_shop_right(self):
        """处理店铺权限"""
        user_id = self.request.state.login_user_id  # 当前申请用户
        post_data = await self.request.json()
        shop_country_id = post_data.get('shop_country_id')
        relation_id = post_data.get('relation_id')
        op_type = post_data.get('op_type')
        shop_country_role = post_data.get('shop_country_role')
        if op_type not in self.op_type:
            return self.r(code=105009, msg='参数错误,翻译')
        if not all([shop_country_id, relation_id, op_type]):
            return self.r(code=105009, msg='参数错误,翻译')
        user_relation = await UserVirtualShopCountryRelationModel.get_user_country_v_shop_one(
            conditions={'shop_country_id': shop_country_id, 'user_id': user_id}
        )
        if not user_relation or user_relation['shop_country_role'] != '3':
            return self.r(code=105010, msg='权限不存在,或者不是管理员，待更换翻译')

        op_relation = await UserVirtualShopCountryRelationModel.get_user_country_v_shop_one(
            conditions={'id': relation_id}
        )
        if not op_relation:
            return self.r(code=105011, msg='权限不存在,待更换翻译')

        handler_name = self.op_type[op_type]
        if shop_country_role:
            op_relation['new_shop_country_role'] = str(shop_country_role)
        handler = getattr(self, handler_name, user_relation)
        return await handler(user_relation, op_relation)

    async def _process_shop_right_remove(self, user_relation, op_relation):
        """移除用户权限"""
        if op_relation['shop_country_role'] == '3':
            return self.r(code=105013, msg='不能移除管理员,切换后 移除， 待翻译')
        if op_relation['shop_country_role'] not in ["1", "2"]:
            return self.r(code=105014, msg='该账号非当前 国家用户 不能变更权限 待翻译')
        # 移除所有权限
        update_op_role = await UserVirtualShopCountryRelationModel.update_conditions(
            conditions={'id': op_relation['id']},
            new_values={
                'shop_country_role': '0',
                'role_user_status': '3',
                'user_apply_status': '0'
            }
        )
        if update_op_role:
            # 发送消息
            to_config = {
                "user_id": op_relation['user_id'],
                "shop_name": op_relation['shop_name'],
                "shop_country_role": op_relation['shop_country_role'],
                "country_code": op_relation['country_code'],
                "shop_country_id": op_relation['shop_country_id'],
                "new_shop_country_role": "0"  # 变更为无权限
            }
            return self.r()
        return self.r(code=105012, msg='移除失败,待更换翻译')

    async def _process_shop_right_switch_admin(self, user_relation, op_relation):
        """切换管理员"""
        # 判断被操作人员 属性：
        if op_relation['shop_country_role'] == '3':
            return self.r(code=105013, msg='不能切换管理员 翻译')
        if op_relation['shop_country_role'] not in ["1", "2"]:
            return self.r(code=105014, msg='该账号非当前 国家用户 不能变更权限 待翻译')
        # 变更角色，
        update_op_role = await UserVirtualShopCountryRelationModel.update_conditions(
            conditions={'id': op_relation['id']},
            new_values={
                'shop_country_role': '3',
            }
        )
        if not update_op_role:
            return self.r(code=105015, msg='变更失败,待更换翻译')
        update_user = await UserVirtualShopCountryRelationModel.update_conditions(
            conditions={'id': user_relation['user_id']},
            new_values={
                'shop_country_role': '2',
            }
        )
        if not update_user:
            return self.r(code=105015, msg='变更失败,待更换翻译')
        # 发送通知
        from_config = {
            "user_id": user_relation['user_id'],
            "shop_name": user_relation['shop_name'],
            "shop_country_role": user_relation['shop_country_role'],
            "country_code": user_relation['country_code'],
            "shop_country_id": user_relation['shop_country_id'],
            "new_shop_country_role": "2",  # 变更为协同
        }
        to_config = {
            "user_id": op_relation['user_id'],
            "shop_name": op_relation['shop_name'],
            "shop_country_role": op_relation['shop_country_role'],
            "country_code": op_relation['country_code'],
            "shop_country_id": op_relation['shop_country_id'],
            "new_shop_country_role": "3"  # 变更为公为管理员
        }
        await ShopRightMessageAction.shop_right_switch_admin_message(from_config, to_config)
        return self.r()

    async def _process_shop_right_change_role(self, user_relation, op_relation):
        """变更角色"""
        if "shop_country_role" not in op_relation or\
                op_relation['shop_country_role'] == op_relation['new_shop_country_role'] or\
                op_relation['new_shop_country_role'] not in ['1', '2']:
            return self.r(code=105013, msg='更新用户全新 需要给新的不一样的权限')
        # 变更角色
        update_result = await UserVirtualShopCountryRelationModel.update_conditions(
            conditions={'id': op_relation['id']},
            new_values={
                'shop_country_role': op_relation['new_shop_country_role'],
            }
        )
        if update_result:
            to_config = {
                "user_id": op_relation['user_id'],
                "shop_name": op_relation['shop_name'],
                "shop_country_role": op_relation['shop_country_role'],
                "country_code": op_relation['country_code'],
                "shop_country_id": op_relation['shop_country_id'],
                "new_shop_country_role": op_relation['new_shop_country_role'],
            }
            await ShopRightMessageAction.change_shop_country_right_message(to_config)
            return self.r()
        return self.r(code=105012, msg='同意失败,待更换翻译')

    async def _process_shop_right_reject(self, user_relation, op_relation):
        """拒绝 权限"""
        update_result = await UserVirtualShopCountryRelationModel.update_conditions(
            conditions={'id': op_relation['id']},
            new_values={
                'shop_country_role': '0',
                'role_user_status': '0',
                'user_apply_status': '2',
                'agree_datetime': get_datetime(),
                'user_apply_info': None
            }
        )
        # 发送同意消息
        if update_result:
            # 查看管理员 信息 发送给被申请人
            to_config = {
                "user_id": op_relation['user_id'],
                "shop_name": op_relation['shop_name'],
                "shop_country_role": op_relation['shop_country_role'],
                "country_code": op_relation['country_code'],
                "shop_country_id": op_relation['shop_country_id']
            }
            # 发送信息
            await ShopRightMessageAction.reject_shop_country_right_message(to_config)
            return self.r()
        return self.r(code=105012, msg='同意失败,待更换翻译')

    async def _process_shop_right_agree(self, user_relation, op_relation):
        """同意 权限"""
        # 更新为同意
        shop_country_role = "2" if op_relation['shop_country_role'] == "6" else "1"
        update_result = await UserVirtualShopCountryRelationModel.update_conditions(
            conditions={'id': op_relation['id']},
            new_values={
                'shop_country_role': shop_country_role,  # 同意
                'role_user_status': '1',
                'user_apply_status': '3',
                'agree_datetime': get_datetime(),
                'user_apply_info': None
            }
        )
        # 发送同意消息
        if update_result:
            # 查看管理员 信息 发送给被申请人
            to_config = {
                "user_id": op_relation['user_id'],
                "shop_name": op_relation['shop_name'],
                "shop_country_role": shop_country_role,
                "country_code": op_relation['country_code'],
                "shop_country_id": op_relation['shop_country_id']
            }
            # 发送信息
            await ShopRightMessageAction.agree_shop_country_right_message(to_config)
            return self.r()
        return self.r(code=105012, msg='同意失败,待更换翻译')

    async def withdraw_shop_right(self):
        """撤回用户权限申请"""
        user_id = self.request.state.login_user_id  # 当前申请用户
        post_data = await self.request.json()
        relation_id = post_data.get('relation_id')
        user_relation = await UserVirtualShopCountryRelationModel.get_user_country_v_shop_one(
            conditions={'id': relation_id, 'user_id': user_id}
        )
        if not user_relation:
            return self.r(code=105006, msg='权限不存在，待更换翻译')
        if user_relation['user_apply_status'] not in ['6', '7']:
            return self.r(code=105007, msg='已经被处理不能撤销')
        shop_country_id = user_relation['shop_country_id']

        # 撤销更新
        update_result = await UserVirtualShopCountryRelationModel.update_conditions(
            conditions={'id': relation_id},
            new_values={
                'user_apply_status': '5',  # 撤销
                'role_user_status': '0',
                'user_apply_datetime': None,
                'user_apply_info': None
            }
        )

        if update_result:
            # 查看管理员 信息
            shop_manager = await UserVirtualShopCountryRelationModel.get_user_country_v_shop_one(
                conditions={'shop_country_id': shop_country_id,
                            'shop_country_role': '3'}
            )
            now_user = await self.get_user_info(user_id=user_id)
            # 撤回消息

            await ShopRightMessageAction.withdraw_shop_country_right_message(
                from_config={
                    "user_id": user_id,
                    "shop_name": user_relation['shop_name'],
                    "email": now_user['email']
                },
                to_config={
                    "user_id": shop_manager['user_id'],
                    "shop_name": shop_manager['shop_name']
                },
                shop_country_id=shop_country_id,
                shop_country_code=shop_manager['country_code']
            )

            # 消息完毕
            return self.r()
        return self.r(code=105006, msg='撤回失败,待更换翻译')

    async def apply_shop_right(self):
        """申请店铺权限"""
        user_id = self.request.state.login_user_id  # 当前申请用户
        post_data = await self.request.json()
        shop_country_id = post_data.get('shop_country_id')
        user_apply_info = post_data.get('user_apply_info')
        user_apply_status = post_data.get('user_apply_status')
        if not shop_country_id or not user_apply_status:
            return self.r(code=300001, msg='Need shop_country_id and user_apply_status')
        # 检查申请权限
        check_right, relation_info = await self.check_applay_right(
            user_id=user_id, shop_country_id=shop_country_id, user_apply_status=user_apply_status
        )
        if not check_right:
            return self.r(**relation_info)
        # 查询用户是否已经申请
            # 申请店铺权限, 查询 国家管理员
        shop_manager = await UserVirtualShopCountryRelationModel.get_user_country_v_shop_one(
            conditions={'shop_country_id': shop_country_id,
                        'shop_country_role': '3'}
        )
        if not shop_manager:
            return self.r(code=105002, msg='当前店铺没有管理员,无法申请,待更换翻译')
        # 更改申请权限
        update_result = await UserVirtualShopCountryRelationModel.update_conditions(
            conditions={'user_id': user_id, "id": relation_info['id']},
            new_values={
                "role_user_status": "2",  # 申请中
                "user_apply_status": user_apply_status,
                "user_apply_datetime": get_datetime(),
                "user_apply_info": user_apply_info  # 申请状态包含申请权限信息
            }
        )
        # 以及发送通知
        if update_result:
            # 发送通知
            now_user = await self.get_user_info(user_id=user_id)
            from_message_config = {
                "user_id": relation_info['user_id'],
                "shop_name": relation_info['shop_name'],
                "user_apply_status": user_apply_status,
                "email": now_user['email']
            }
            to_message_config = {
                "user_id": shop_manager['user_id'],
                "shop_name": shop_manager['shop_name'],
                "user_apply_info": user_apply_info
            }
            await ShopRightMessageAction.apply_shop_country_right_message(
                from_config=from_message_config,
                to_config=to_message_config,
                shop_country_id=shop_country_id,
                shop_country_code=shop_manager['country_code']
            )
            # 通知完毕
            return self.r()
        return self.r(code=105006, msg='申请失败,待更换翻译')

    async def check_applay_right(self, user_id, shop_country_id, user_apply_status):
        """检查申请权限"""
        relation_info = await UserVirtualShopCountryRelationModel.get_user_country_v_shop_one(
            conditions={'user_id': user_id, 'shop_country_id': shop_country_id})
        if relation_info['user_apply_status'] in ['6', '7']:
            return False, {'code': 105003, 'msg': '提示:你已经申请了待更换翻译'}
        # 如果是管理员
        if relation_info['shop_country_role'] != "0":
            return False, {'code': 105005, 'msg': '提示:你已经是对应的权限了待更换翻译'}
        if user_apply_status not in ['6', '7']:
            return False, {'code': 105004, 'msg': '提示:请选择申请状态异常，参数应该是协同或者只读待更换翻译'}

        return True, relation_info

    async def get_my_shop_right(self):
        """获取当前用户对-店铺权限
            分为 5类 激活人 (看到所有人和申请)
                    协同(看到所有人)
                    只读 (看到所有人)
                    被取消权限  
                    无关联权限 
            返回 店铺(自定义店铺名称)
                    国家-线上店铺名称
                        权限
        """
        user_id = self.request.state.login_user_id
        # 获取用户店铺权限

        # 查询用户所有的店铺列表 和权限
        i_have_right = await self._statistics_shop_country_right(user_id)
        # 查询所有的  国家权限用户
        i_have_right = await self._statistics_other_shop_country_right(i_have_right, user_id)
        # 整合店铺输出列表
        shop_country_list = i_have_right['shop_country_list']
        shop_result = {}
        # 循环输出 并关联店铺信息
        for shop_country in shop_country_list:
            shop_name = shop_country['shop_name']
            sp_area_code = shop_country['sp_area_code']
            country_id = shop_country['country_id']
            country_name = shop_country['ad_shop_name']
            country_code = shop_country['country_code']
            if shop_name not in shop_result:
                shop_result[shop_name] = {}
            if sp_area_code not in shop_result[shop_name]:
                shop_result[shop_name][sp_area_code] = {}
            if country_code not in shop_result[shop_name][sp_area_code]:
                shop_result[shop_name][sp_area_code][country_code] = {
                    "login_user_id": user_id,
                    "login_user_role": shop_country['shop_country_role'],
                    "country_id": country_id,
                    "country_name": country_name,
                    "user_list": i_have_right['shop_user'][country_id]
                }
        result = {
            "shop_right": shop_result,
            "shop_country_role": USER_SHOP_COUNTRY_ROLE,
            "role_user_status": USER_ROLE_USER_STATUS,
            "user_apply_status": USER_APPLY_STATUS
        }

        return self.r(data=result)

    async def _statistics_other_shop_country_right(self, i_have_right, user_id):
        """查询其他用户"""
        country_ids = list(i_have_right["active_list"].keys(
        )) + list(i_have_right["co_list"].keys()) + list(i_have_right["read_list"].keys())
        i_have_right['shop_user'] = {}
        if country_ids:
            # 查询所 可以看 的用户
            user_list = await UserVirtualShopCountryRelationModel.get_user_relation(
                conditions={'shop_country_id': ['in', country_ids]},
                user_model=UserModel
            )
            # 超时 定义
            over_time_apply_id = []
            for user in user_list:
                # 追加到国家中
                if user['shop_country_id'] not in i_have_right["shop_user"]:
                    i_have_right['shop_user'][user['shop_country_id']] = []
                if user['shop_country_id'] in i_have_right["active_list"] \
                        and user['role_user_status'] != '0':  # 只要不是无关系
                    if user['role_user_status'] == "2":
                        # 如果是申请中 判断是否大于3天
                        if get_diff_seconds(user['user_apply_datetime']) > 3 * 24 * 60 * 60:
                            # 超时 加入超时列表
                            over_time_apply_id.append(user['id'])
                            continue
                    # 非申请中的
                    i_have_right['shop_user'][user['shop_country_id']].append(
                        user)
                elif user['shop_country_id'] in i_have_right["co_list"] \
                        and user['role_user_status'] == "1":
                    # 协同 的 用户
                    i_have_right['shop_user'][user['shop_country_id']].append(
                        user)
                elif user['shop_country_id'] in i_have_right["read_list"] \
                        and user['role_user_status'] == "1":
                    # 只读的已经加入的
                    i_have_right['shop_user'][user['shop_country_id']].append(
                        user)
            # 重置超时申请
            if over_time_apply_id:
                await UserVirtualShopCountryRelationModel.update_conditions(
                    conditions={'id': ['in', over_time_apply_id]},
                    new_values={
                        'shop_country_role_apply': '0',
                        'user_apply_status': '4',
                        'role_user_status': '0'
                    }
                )

        # 处理 无关联权限
        none_country_ids = list(i_have_right["none_list"].keys(
        )) + list(i_have_right["cancel_list"].keys())
        if none_country_ids:
            # 查询所  只能看到自己
            user_list = await UserVirtualShopCountryRelationModel.get_user_relation(
                conditions={'shop_country_id': [
                    'in', none_country_ids], 'user_id': user_id},
                user_model=UserModel
            )
            for user in user_list:
                # 追加到国家中
                if user['shop_country_id'] not in i_have_right["shop_user"]:
                    i_have_right['shop_user'][user['shop_country_id']] = []
                i_have_right['shop_user'][user['shop_country_id']].append(user)

        return i_have_right

    async def _statistics_shop_country_right(self, user_id):
        """统计店铺 一个人的权限"""
        shop_country_list = await UserVirtualShopCountryRelationModel.get_user_shop_list(
            user_id=user_id
        )
        result = {
            "active_list": {},
            "co_list": {},
            "read_list": {},
            "cancel_list": {},
            "none_list": {},
            "shop_country_list": shop_country_list
        }
        for item in shop_country_list:
            if item['shop_country_role'] == "3":  # 管理员
                result["active_list"][item['country_id']] = item
            elif item['shop_country_role'] == "2":  # 协同
                result["co_list"][item['country_id']] = item
            elif item['shop_country_role'] == "1":  # 只读
                result["read_list"][item['country_id']] = item
            elif item['shop_country_role'] == "0" and item['role_user_status'] == "3":  # 被取消权限
                result["cancel_list"][item['country_id']] = item
            else:  # 无关联权限
                result["none_list"][item['country_id']] = item

        return result

    async def user_invate_send_email(self):
        """邀请发送邮件"""
        from_user_id = self.request.state.login_user_id
        # 获取post 参数
        post_data = await self.request.json()
        country_id = post_data.get("country_id")
        email = post_data.get("email")
        country_role = post_data.get("country_role", "0")

        if not country_id or not email or not country_role or country_role not in ["1", "2"]:
            return self.r(
                code=104001,
                msg="Parameter error. country_id,email, country_role are required"
            )

        #  获取 当前 用户权限
        relation_info = await UserVirtualShopCountryRelationModel.get_user_country_v_shop_one(
            conditions={
                "user_id": from_user_id,
                "shop_country_id": country_id,
            }
        )
        if not relation_info or int(relation_info['shop_country_role']) < int(country_role):
            return self.r(code=104001, msg="You don't have sufficient authority to invite")
        # 检查被邀请人是否 已经有权限了
        email_user = await self.get_user_info(email=email)
        if email_user:
            # 如果已经注册 检查是否有对应的权限
            email_relation_info = await UserVirtualShopCountryRelationModel.get_one_conditions(
                conditions={
                    "user_id": email_user['id'],
                    "shop_country_id": country_id,
                }
            )
            if email_relation_info:  # 如果已经有相关关系
                return self.r(code=104001, msg="The user already has the corresponding permissions")

        # 获取当前用户信息
        user_info = await UserModel.get_one_conditions(
            conditions={
                "id": from_user_id,
            }
        )
        # 获取店铺名称
        shop_country_info = await AmazonShopCountryModel.get_one_conditions(
            conditions={
                "id": country_id,
            }
        )
        if not user_info or not shop_country_info:
            return self.r(
                code=104001,
                msg="Parameter error. user_info or shop_country_info is None"
            )

        # 生成发送邮件记录
        invate_code = get_random_code() + "_" + str(from_user_id)
        # 查询新的
        old_invate = await UserInvateByEmailModel.get_one_conditions(
            conditions={
                "from_user_id": from_user_id,
                "country_id": country_id,
                "email": email,
            }
        )
        new_value = {
            "country_role": str(country_role),
            "invate_datetime": get_datetime(),
            "invate_code": invate_code,
            "code_expire_timestmp": get_datetime("timestamp") + 3600 * 24 * 3,
        }
        if old_invate:
            # 更新
            result = await UserInvateByEmailModel.update_conditions(
                conditions={
                    "id": old_invate['id'],
                },
                new_values=new_value
            )
        else:
            new_value.update({
                "from_user_id": from_user_id,
                "country_id": country_id,
                "email": email,
            })
            result = await UserInvateByEmailModel.add_one(
                data=new_value
            )
        # 发送邮件
        if result:
            # 发送邮件
            result = EmailFunction.setn_invate_email(
                from_user_name=user_info['nick_name'],
                email=email,
                invate_code=invate_code,
                shop_name=shop_country_info['ad_shop_name'],
                shop_country=shop_country_info['country_code'],
                country_role=country_role,
            )
            if result:
                # 新增发送通知
                await self._invate_message(
                    relation_info,
                    country_role=country_role,
                    email=email,
                    country_id=country_id
                )
                # 通知完毕
                return self.r(msg="Invitation email sent successfully")
            return self.r(code=104001, msg="Invitation email sent failed")

        return self.r(code=104001, msg="Invitation info save failed")

    async def _invate_message(self, relation_info, country_role, email, country_id):
        """发送邀请通知"""
        if relation_info['shop_country_role'] == "3":
            # 如果是管理员 邀请
            admin_config = {  # 管理员自己收到信息
                "user_id": relation_info['user_id'],
                "shop_country_id": country_id,
                "shop_name": relation_info['shop_name'],
                "country_code": relation_info['country_code'],
                "country_role": country_role,
                "email": email
            }
            collaborator_config = None  # 协同 不发送
        else:
            # 如果是协同邀请
            collaborator_config = {
                "user_id": relation_info['user_id'],
                "shop_country_id": country_id,
                "shop_name": relation_info['shop_name'],
                "country_code": relation_info['country_code'],
                "country_role": country_role,
                "email": email
            }
            # 需要查询管理员
            admin_info = await UserVirtualShopCountryRelationModel.get_user_country_v_shop_one(
                conditions={
                    "shop_country_id": country_id,
                    "shop_country_role": "3"
                }
            )
            admin_config = {
                "user_id": admin_info['user_id'],
                "shop_country_id": country_id,
                "shop_name": admin_info['shop_name'],
                "country_code": country_role,
                "email": email,
                "from_email": relation_info['email'],
            }
        # 发送信息
        await InvateUserMessageAction.add_invate_user_message(
            admin_config=admin_config,
            collaborator_config=collaborator_config
        )

    async def verify_email_invate_code(self):
        """验证邮箱邀请码
        (1) 是否有效
        (2) 是否是登录的用户
        (3) 是否已经有了店铺关联关系
        (4) 
        """
        post_data = await self.request.json()
        invate_code = post_data.get("invate_code", None)
        if not invate_code:
            return self.r(code=104016, msg="Parameter error. invate_code is required")
        # 验证 邀请吗
        invate_info = await self._get_invate_code_info(invate_code)
        if not invate_info:
            return self.r(code=104016, msg="Invitation code is invalid")
        # 看是否有登录用户
        if self.request.state.login_user_id:
            # 获取当前用户 是否一致
            login_user_info = await self.get_user_info(user_id=self.request.state.login_user_id)
            if login_user_info['email'] != invate_info['email']:
                # 当前登录用户与 被邀请的不一致
                return self.r(
                    code=200,
                    data="",
                    msg="Click email and current login user are inconsistent"
                )
            # 如果是一致 的，则直接添加 关联关系
            return await self._add_new_relation(invate_info, login_user_info)
        password = post_data.get("password", None)
        # 如果没有登录
        old_user_info = await self.get_user_info(email=invate_info['email'])
        if not old_user_info:
            # 表示是新用户， 需要 设置登录密码
            if password:
                new_user_info = await self._create_new_user(invate_info, password)
                return await self._add_new_relation(invate_info, new_user_info)
            return self.r(code=200, data="new_user", msg="Need set new password")
        # 如果是老用户
        if password:
            if old_user_info['password'] == sha1(password):
                return await self._add_new_relation(invate_info, old_user_info)
            return self.r(code=104018, msg="Password error")
        return self.r(code=200, data="old_user", msg="Need login")

    async def _create_new_user(self, invate_info, password):
        """创建新用户"""
        new_user = {
            "email": invate_info['email'],
            "password": sha1(password),
            "nick_name": invate_info['email'].split("@")[0],
            "reg_datetime": get_datetime(),
        }
        new_user_id = await UserModel.add_one(
            data=new_user
        )
        new_user['id'] = new_user_id
        return new_user

    async def _delte_invate_info(self, invate_info):
        """删除邀请信息"""
        await UserInvateByEmailModel.delete_conditions(
            conditions={
                "id": invate_info['id'],
            }
        )

    async def _add_new_relation(self, invate_info, invate_user):
        """添加新的关联关系"""
        country_id = invate_info['country_id']
        relation_info = await UserVirtualShopCountryRelationModel.get_user_country_v_shop_one(
            conditions={
                "user_id": invate_user['id'],
                "shop_country_id": country_id,
            }
        )
        if relation_info:
            shop_name = relation_info['shop_name']
            # 如果已经存在关系
            if int(relation_info['shop_country_role']) < int(invate_info['country_role']):
                new_data = {
                    "shop_country_role": invate_info['country_role'],
                    "role_user_status": "1",
                    "user_apply_status": "3"
                }
                if relation_info['role_user_status'] != "1":
                    # 如果原来没有加入
                    new_data['agree_datetime'] = get_datetime()
                result = await UserVirtualShopCountryRelationModel.update_conditions(
                    conditions={
                        "id": relation_info['id'],
                    },
                    new_values=new_data
                )
                if result:
                    await self._delte_invate_info(invate_info)
                    # 更新权限成功
                    relation_info['shop_country_role'] = invate_info['country_role']
                    await self._invate_join_success_message(
                        new_relation=relation_info,
                        invate_info=invate_info
                    )
                    # 发送 通知
                    return self.r(
                        code=200,
                        data="update",
                        msg="The original permission already exists and has been updated"
                    )
                return self.r(code=104018, msg="Update permission failed")
            #  已经存在对应权限
            await self._delte_invate_info(invate_info)
            return self.r(code=200, data="haved",
                          msg="The permission already exists")
        # 如果不怒转关系，新增店铺
        shop_name = "Temp shop name" + \
            str(invate_user['id']) + "_" + str(get_random_code(5)),
        new_shop_id = await UserVitualShopModel.add_one(
            data={
                "user_id": invate_user['id'],
                "shop_name": shop_name,
            }
        )

        # 新增虚拟店铺以及关联关系
        new_data = {
            "user_vitrual_shop_id": new_shop_id,
            "user_id": invate_user['id'],
            "shop_country_relation_addtime": get_datetime(),
            "shop_country_role": invate_info['country_role'],
            "role_user_status": "1",
            "user_apply_status": "3",
            "agree_datetime": get_datetime(),
            "shop_country_id": invate_info['country_id'],
        }
        result = await UserVirtualShopCountryRelationModel.add_one(
            data=new_data
        )
        if result:
            await self._delte_invate_info(invate_info)
            # 发送通知
            new_data['id'] = result
            new_data['shop_name'] = shop_name
            await self._invate_join_success_message(new_relation=new_data, invate_info=invate_info)
            return self.r(code=200, data="add_new", msg="Success")
        return self.r(code=104018, msg="Add permission failed")

    async def _invate_join_success_message(self, new_relation, invate_info):
        """邀请成功后的消息发送"""
        new_user_config = {
            "user_id": new_relation['user_id'],
            "email": new_relation['email'],
            "shop_country_id": new_relation['shop_country_id'],
            "shop_country_role": new_relation['shop_country_role'],
            "shop_name": new_relation['shop_name'],
        }
        # 店铺 其他人 信息
        old_relation = await UserVirtualShopCountryRelationModel.get_user_country_v_shop_list(
            conditions={
                "shop_country_id": new_relation['shop_country_id'],
                "id": ['!=', new_relation['id']],
            }
        )
        other_user = []
        if old_relation:
            for item in old_relation:
                # 其他用户信息
                other_user.append(
                    {
                        "user_id": item['user_id'],
                        "shop_name": item['shop_name'],
                    }
                )
        # 邀请者信息
        from_invite_user = await UserModel.get_one_conditions(
            conditions={
                "id": invate_info['from_user_id'],
            }
        )
        # 发送信息
        await InvateUserMessageAction.accept_invate_user_message(
            new_user_config=new_user_config,
            other_user_config=other_user,
            from_invite_user=from_invite_user,
        )

    async def _get_invate_code_info(self, invate_code):
        """根据邀请码 获取 邀请信息"""
        invate_info = await UserInvateByEmailModel.get_one_conditions(
            conditions={
                "invate_code": invate_code,
            }
        )
        if not invate_info or get_datetime("timestamp") > invate_info['code_expire_timestmp']:
            return False
        return invate_info

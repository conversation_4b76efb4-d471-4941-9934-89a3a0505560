# -*- coding: utf-8 -*-
"""用户权限 消息处理"""
from app.controllers.user.public.message_action import MessageAction
# from app.utils.function import get_datetime
from app.utils.const import USER_SHOP_COUNTRY_ROLE


class ShopRightMessageAction(MessageAction):
    """用户权限消息"""

    @classmethod
    async def shop_right_remove_message(cls,  to_config: dict):
        """移除权限"""
        to_message = {
            "user_id": to_config.get("user_id"),
            "area_shop_id": to_config.get("shop_country_id"),
            "message_type": "REMOVE_R",  # 移除权限
            "message_title": (
                f"【{to_config.get('shop_name')}】"
                f"【{to_config.get('country_code')}】Permission removed"
            ),
            "message_content": (
                f"You have been removed from 【{to_config.get('shop_name')}】"
                f" ({to_config.get('country_code')})."
                " You no longer have access to this site."
            ),
        }
        # 添加信息
        await cls.add_user_message(**to_message)

    @classmethod
    async def shop_right_switch_admin_message(cls, from_config: dict, to_config: dict):
        """更换管理员"""
        from_message = {
            "user_id": from_config.get("user_id"),
            "area_shop_id": from_config.get("shop_country_id"),
            "message_type": "SWITCH_A",  # 变更权限 管理员
            "message_title": (
                f"【{from_config.get('shop_name')}】"
                f"【{from_config.get('country_code')}】Permission changed"
            ),
            "message_content": (
                f"Your role has been updated to"
                f" {USER_SHOP_COUNTRY_ROLE.get(from_config.get('new_shop_country_role'))}"
                f"for 【{from_config.get('shop_name')}】 ({from_config.get('country_code')})."
            ),
        }
        to_message = {
            "user_id": to_config.get("user_id"),
            "area_shop_id": to_config.get("shop_country_id"),
            "message_type": "SWITCH_A",  # 变更权限 管理员
            "message_title": (
                f"【{to_config.get('shop_name')}】"
                f"【{to_config.get('country_code')}】Permission changed"
            ),
            "message_content": (
                f"Your role has been updated to"
                f" {USER_SHOP_COUNTRY_ROLE.get(to_config.get('new_shop_country_role'))}"
                f"for 【{to_config.get('shop_name')}】 ({to_config.get('country_code')})."
            ),
        }
        # 添加信息
        await cls.add_user_message(**from_message)
        await cls.add_user_message(**to_message)

    @classmethod
    async def change_shop_country_right_message(cls, to_config):
        """变更店铺权限-对应消息"""
        user_id = to_config.get("user_id")
        shop_name = to_config.get("shop_name")
        shop_country_id = to_config.get("shop_country_id")
        country_code = to_config.get("country_code")
        # shop_country_role = USER_SHOP_COUNTRY_ROLE.get(
        #     to_config.get("shop_country_role")) # 老的
        new_shop_country_role = USER_SHOP_COUNTRY_ROLE.get(
            to_config.get("new_shop_country_role"))
        # 变更消息-只给被修改的人
        to_message = {
            "user_id": user_id,
            "area_shop_id": shop_country_id,
            "message_type": "CHANGE_R",  # 变更权限
            "message_title": f"【{shop_name}】【{country_code}】Permission changed ",
            "message_content": (
                f"Your role has been updated to {new_shop_country_role}"
                f"for 【{shop_name}】 ({country_code})."
            ),
        }
        await cls.add_user_message(**to_message)

    @classmethod
    async def reject_shop_country_right_message(cls, to_config):
        """拒绝店铺权限-对应消息"""
        user_id = to_config.get("user_id")
        shop_name = to_config.get("shop_name")
        shop_country_role = to_config.get("shop_country_role")
        shop_country_id = to_config.get("shop_country_id")
        country_code = to_config.get("country_code")
        shop_country_role = USER_SHOP_COUNTRY_ROLE.get(shop_country_role)
        to_message = {
            "user_id": user_id,
            "area_shop_id": shop_country_id,
            "message_type": "REJECT_R",  # 拒绝权限
            "message_title": f"【{shop_name}】【{country_code}】Permission application rejected ",
            "message_content": (
                f"Your request to join 【{shop_name}】 ({country_code}) as"
                f" a {shop_country_role} has been rejected."
            ),
        }
        await cls.add_user_message(**to_message)

    @classmethod
    async def agree_shop_country_right_message(cls, to_config):
        """同意店铺权限-对应消息"""
        user_id = to_config.get("user_id")
        shop_name = to_config.get("shop_name")
        shop_country_role = to_config.get("shop_country_role")
        shop_country_id = to_config.get("shop_country_id")
        country_code = to_config.get("country_code")
        shop_country_role = USER_SHOP_COUNTRY_ROLE.get(shop_country_role)
        to_message = {
            "user_id": user_id,
            "area_shop_id": shop_country_id,
            "message_type": "AGREE_RI",  # 同意权限
            "message_title": f"【{shop_name}】【{country_code}】Permission application approved ",
            "message_content": (
                f"You have been approved as a {shop_country_role} for 【{shop_name}】 ({country_code}). "
                f"You now have access to the site."
            ),
        }
        await cls.add_user_message(**to_message)

    @classmethod
    async def withdraw_shop_country_right_message(
        cls,
        from_config,
        to_config,
        shop_country_id,
        shop_country_code,
    ):
        """撤回店铺权限-对应消息"""
        from_user = from_config.get("user_id")
        from_shop = from_config.get("shop_name")
        from_email = from_config.get("email")
        to_user = to_config.get("user_id")
        to_shop = to_config.get("shop_name")
        from_message = {
            "user_id": from_user,
            "area_shop_id": shop_country_id,
            "message_type": "WITHDRAW",  # 申请权限-撤销
            "message_title": f"【{from_shop}】【{shop_country_code}】权限申请申请撤销",
            "message_content":
            f"You have withdrawn your request to join 【{to_shop}】 ({shop_country_code}).",
        }
        to_message = {
            "user_id": to_user,
            "area_shop_id": shop_country_id,
            "message_type": "WITHDRAW",  # 申请权限-撤销
            "message_title": f"{from_email}【{to_shop}】【{shop_country_code}】权限申请申请撤销",
            "message_content":
            f"{from_email} has withdrawn your request to join 【{to_shop}】 ({shop_country_code}).",
        }
        # 添加消息
        await cls.add_user_message(**from_message)
        await cls.add_user_message(**to_message)

    @classmethod
    async def apply_shop_country_right_message(
        cls,
        from_config,
        to_config,
        shop_country_id,
        shop_country_code,
    ):
        """申请店铺权限-对应消息"""
        from_user = from_config.get("user_id")
        from_shop = from_config.get("shop_name")
        from_email = from_config.get("email")
        user_apply_status = from_config.get("user_apply_status")
        to_user = to_config.get("user_id")
        to_shop = to_config.get("shop_name")
        user_apply_info = to_config.get("user_apply_info", "")
        if user_apply_info:
            user_apply_info = "Note:" + user_apply_info
        if user_apply_status == "6":
            user_apply_status = USER_SHOP_COUNTRY_ROLE['2']  # 协同
        elif user_apply_status == "7":
            user_apply_status = USER_SHOP_COUNTRY_ROLE['1']  # 只读
        from_message = {
            "user_id": from_user,
            "area_shop_id": shop_country_id,
            "message_type": "APPLY_RI",  # 申请权限
            "message_title": f"【{from_shop}】【{shop_country_code}】权限申请已提交",
            "message_content":
                (
                    f"You have requested to join 【{to_shop}】"
                    f" ({shop_country_code}) as a {user_apply_status}. "
                    "Waiting for site owner approval."
            ),
        }
        to_message = {
            "user_id": to_user,
            "area_shop_id": shop_country_id,
            "message_type": "APPLY_RI",  # 申请权限
            "message_title": f"{from_email}【{to_shop}】【{shop_country_code}】权限申请待处理",
            "message_content":
                (
                    f"{from_email} has requested to join 【{to_shop}】"
                    f" ({shop_country_code}) as a {user_apply_status}. "
                    f"{user_apply_info}"
            ),
        }
        await cls.add_user_message(**from_message)
        await cls.add_user_message(**to_message)

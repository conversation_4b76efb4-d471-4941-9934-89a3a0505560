# -*- coding: utf-8 -*-
""" 增加这里是独立的通过亚马逊AD授权获取账户信息 """
# @Time    : 2025-02-11 16:10:44
# <AUTHOR> wang<PERSON><PERSON>uyang
import json
import requests
from setting import AMAZON_AD_PARTNER_ID, ACCESS_TOKEN_DAILI_URL

DOMAIN_DICT = {
    "NA": "https://advertising-api.amazon.com",
    "EU": "https://advertising-api-eu.amazon.com",
    "FE": "https://advertising-api-fe.amazon.com"
}


class AmazonAdAuthGetAccount():
    """AD授权获取账户信息"""
    method = None
    data = None
    domain = None
    uri_path = None
    headers = None

    def __init__(self, region, access_token):
        """初始化对象"""
        self.timeout = 30
        self.region = region
        self.access_token = access_token
        self.client_id = AMAZON_AD_PARTNER_ID
        self.proxies = {"https": ACCESS_TOKEN_DAILI_URL}
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.access_token}",
            "Amazon-Advertising-API-ClientId": self.client_id
        }

    def _execute(self):
        """执行请求"""
        # self.headers["Content-Type"] = "application/vnd.createasyncreportrequest.v3+json"
        url = self.domain + self.uri_path
        if self.method == "delete":
            response = requests.delete(
                url, headers=self.headers, timeout=self.timeout).text
            return response
        if self.data:
            self.data = json.dumps(self.data)
        if self.region != "FE":
            self.proxies = {}

        response = requests.request(
            self.method,
            url,
            headers=self.headers,
            data=self.data,
            timeout=self.timeout,
            proxies=self.proxies).json()
        return response

    def get_profiles(self):
        """获取账户信息"""
        return [
            {
                "profileId": *********,
                "countryCode": "CA",
                "currencyCode": "CAD",
                "timezone": "America/Los_Angeles",
                "accountInfo": {
                    "marketplaceStringId": "A2EUQ1WTGCTBG2",
                    "id": "selling_partner_id_test1",
                    "type": "seller",
                    "name": "Name of the Account",
                    "validPaymentMethod": False
                }
            }
        ]

        self.domain = DOMAIN_DICT.get(self.region, "")
        if not self.domain:
            return False
        self.uri_path = "/v2/profiles"
        self.method = "get"
        return self._execute()

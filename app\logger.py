# -*- coding: utf-8 -*-
"""日志类"""
import logging
import os
from logging.handlers import TimedRotatingFileHandler
from app.utils.function import get_today
from setting import LOG_DIR


class Logger():
    """日志类"""
    _instance = {}
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    log_level = logging.INFO
    log_name = None

    @classmethod
    def get_instance(cls, log_name="run"):
        """获取日志实例"""
        if log_name not in cls._instance:
            cls._instance[log_name] = cls.make_logger(log_name)
        return cls._instance[log_name]

    @classmethod
    def make_logger(cls, log_name):
        """创建日志实例"""
        try:
            # 创建按日期分类的日志目录
            date_dir = cls._make_date_log_dir()
            logger_obj = logging.getLogger(log_name)

            # 清除已有的处理器，避免重复添加
            logger_obj.handlers.clear()
            logger_obj.setLevel(cls.log_level)

            # 构建日志文件路径：项目根目录/log/日期年月日/日志文件名.log
            log_file = os.path.join(date_dir, log_name + '.log')

            # 确保目录存在
            os.makedirs(os.path.dirname(log_file), exist_ok=True)

            # 创建文件处理器（使用普通FileHandler确保文件立即创建）
            file_handler = logging.FileHandler(
                log_file, mode='a', encoding='utf-8')
            file_handler.setLevel(cls.log_level)
            formatter = logging.Formatter(cls.log_format)
            file_handler.setFormatter(formatter)
            logger_obj.addHandler(file_handler)

            # 添加控制台输出（用于调试）
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger_obj.addHandler(console_handler)

            return logger_obj

        except Exception as e:
            # 如果文件创建失败，至少返回一个控制台日志器
            logger_obj = logging.getLogger(log_name)
            logger_obj.handlers.clear()
            logger_obj.setLevel(cls.log_level)
            console_handler = logging.StreamHandler()
            formatter = logging.Formatter(cls.log_format)
            console_handler.setFormatter(formatter)
            logger_obj.addHandler(console_handler)
            return logger_obj

    def change_log_file(self, logger_handler, new_filename):
        """改变日志文件"""
        handlers = logger_handler.handlers[:]
        for handler in handlers:
            if isinstance(handler, logging.FileHandler):
                handler.close()
                logger_handler.removeHandler(handler)

        new_handler = logging.FileHandler(
            new_filename, mode='a', encoding='utf-8')
        new_handler.setLevel(self.log_level)
        formatter = logging.Formatter(self.log_format)
        new_handler.setFormatter(formatter)
        logger_handler.addHandler(new_handler)

    @classmethod
    def _make_date_log_dir(cls):
        """创建按日期分类的日志目录"""
        try:
            # 获取当前日期，格式为YYYYMMDD
            current_date = get_today()
            date_log_dir = os.path.join(LOG_DIR, current_date)

            # 如果日期目录不存在则创建
            if not os.path.exists(date_log_dir):
                os.makedirs(date_log_dir)
            return date_log_dir
        except Exception as e:
            # 如果创建失败，返回基础log目录
            return LOG_DIR

    @classmethod
    def _make_log_dir(cls):
        """创建日志目录"""
        if os.path.exists(LOG_DIR) is False:
            os.mkdir(LOG_DIR)


class CustomTimedRotatingFileHandler(TimedRotatingFileHandler):
    """自定义的按时间轮转文件处理器，支持按日期目录分类"""

    def __init__(self, filename, when='h', interval=1, backupCount=0,
                 encoding=None, delay=False, utc=False, atTime=None, log_name='run'):
        self.log_name = log_name
        super().__init__(filename, when, interval, backupCount, encoding, delay, utc, atTime)

    def doRollover(self):
        """执行日志轮转时，确保新的日志文件在正确的日期目录中"""
        if self.stream:
            self.stream.close()
            self.stream = None

        # 获取新的日期目录
        current_date = get_today()
        new_date_dir = os.path.join(LOG_DIR, current_date)

        # 创建新的日期目录（如果不存在）
        if not os.path.exists(new_date_dir):
            os.makedirs(new_date_dir)

        # 更新baseFilename到新的日期目录
        self.baseFilename = os.path.join(new_date_dir, self.log_name + '.log')

        # 如果延迟打开，不需要立即打开文件
        if not self.delay:
            self.stream = self._open()

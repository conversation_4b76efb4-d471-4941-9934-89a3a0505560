# -*- coding: utf-8 -*-
"""python 基础接口类"""
from functools import wraps
from app.controllers.base_controller import BaseController
from app.verify_function import auth_error
from app.utils.function import sha1, get_datetime
from setting import DATA_API_TOKEN_SECRET, DEBUG


class BaseData(BaseController):
    """基础类"""

    @staticmethod
    def verify(f):
        """验证token"""
        @wraps(f)
        async def wrapper(self, *args, **kwargs):
            # 从 self.request 获取请求对象（继承自 BaseController）
            request = self.request
            token = request.headers.get('token')
            timestamp = request.headers.get('timestamp')
            if DEBUG:
                return await f(self, *args, **kwargs)
            if token is None or timestamp is None:
                raise auth_error(201, "Missing token")
            if abs(get_datetime("timestamp") - int(timestamp)) > 300:
                # 过期
                raise auth_error(201, "timestamp error")
            if token != sha1(DATA_API_TOKEN_SECRET + str(timestamp) + DATA_API_TOKEN_SECRET):
                raise auth_error(201, "token error")

            # 调用原始方法
            return await f(self, *args, **kwargs)
        return wrapper

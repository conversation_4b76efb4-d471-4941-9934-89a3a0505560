# -*- coding: utf-8 -*-
"""用户注册相关model"""

from sqlalchemy import Column, Integer, String, DateTime
from app.base_model import BaseModel


class UserModel(BaseModel):
    """用户模型"""
    __tablename__ = BaseModel.table_prefix + "user"

    id = Column(Integer, primary_key=True,
                autoincrement=True, comment="Primary Key")
    nick_name = Column(String(100), nullable=False, comment="昵称")
    email = Column(String(100), nullable=False, comment="email")
    google_joint_id = Column(
        String(100), nullable=True, comment="google 联合 id")
    password = Column(String(63), nullable=False, comment="password")
    last_login_time = Column(
        DateTime, nullable=True, comment="最后登录时间")
    google_joint_other = Column(
        String(100), nullable=True, comment="google 联合其他信息")
    reg_datetime = Column(DateTime, nullable=False, comment="注册时间")
    # 忘记密码 字段
    reset_password_url_code = Column(
        String(64), nullable=True, comment="重置密码 url code")
    reset_password_url_timeout = Column(
        Integer, nullable=True, comment="重置密码 url code 过期时间戳")

    company_name = Column(
        String(256), nullable=True, comment="公司名称")


class UserLogModel(BaseModel):
    """用户日志"""
    __tablename__ = BaseModel.table_prefix + "user_log"

    id = Column(Integer, primary_key=True,
                autoincrement=True, comment="Primary Key")
    area_shop_id = Column(Integer, nullable=False, comment="关联店铺区域id")
    log_type = Column(String(100), nullable=False, comment="日志类型")
    log_content = Column(String(100), nullable=False, comment="日志内容")
    log_datetime = Column(DateTime, nullable=False, comment="日志时间")


class UserLogRelationModel(BaseModel):
    """用户日志 用户关系表"""
    __tablename__ = BaseModel.table_prefix + "user_log_relation"

    id = Column(Integer, primary_key=True,
                autoincrement=True, comment="Primary Key")
    user_id = Column(Integer, nullable=False, comment="用户id")
    log_id = Column(Integer, nullable=False, comment="日志id")
    is_read = Column(Integer, nullable=False, comment="是否读了")
    read_datetime = Column(DateTime, nullable=False, comment="读取时间")


class UserRegSendTimesModel(BaseModel):
    """用户注册发送次数模型"""
    __tablename__ = BaseModel.table_prefix + "user_reg_send_times"

    id = Column(Integer, primary_key=True,
                autoincrement=True, comment="Primary Key")
    session_id = Column(String(36), nullable=False, comment="sessionid")
    times = Column(Integer, nullable=False, default=1, comment="发送次数")
    last_update_timestamp = Column(
        Integer, nullable=True, default=None, comment="最后更新时间戳")


class UserRegTempCodeModel(BaseModel):
    """用户注册临时Code模型"""
    __tablename__ = BaseModel.table_prefix + "user_reg_temp_code"

    id = Column(Integer, primary_key=True,
                autoincrement=True, comment="自动增长 邮箱临时 code")
    email = Column(String(100), nullable=False, comment="email")
    code = Column(String(6), nullable=True, comment="code")
    first_time = Column(Integer, nullable=True, comment="首次发送时间戳")
    send_times = Column(Integer, nullable=False, default=1, comment="发送次数")


class UserInvateByEmailModel(BaseModel):
    """用户邀请注册模型"""
    __tablename__ = BaseModel.table_prefix + "user_invate_by_email"

    id = Column(Integer, primary_key=True,
                autoincrement=True, comment="自动增长 邀请注册模型")
    from_user_id = Column(Integer, nullable=False, comment="邀请人id")
    country_id = Column(Integer, nullable=False, comment="授权国家")
    email = Column(String(100), nullable=False, comment="email")
    country_role = Column(Integer, nullable=False, comment="授权角色")
    invate_datetime = Column(DateTime, nullable=False, comment="邀请时间时间")
    invate_code = Column(String(6), nullable=True, comment="邀请码")
    code_expire_timestmp = Column(Integer, nullable=True, comment="邀请码过期时间戳")
    send_email_flag = Column(Integer, nullable=True,
                             default=0, comment="是否发送邮件")

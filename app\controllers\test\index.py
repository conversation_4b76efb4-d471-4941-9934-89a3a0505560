# -*- coding: utf-8 -*-
"""Test basic controller"""
from app.controllers.base_controller import BaseController
from app.controllers.user.public.message_action import MessageAction


class TestController(BaseController):
    """测试控制器"""

    async def index(self):
        """测试方法"""
        result = self.request.state.login_user_id
        # sql = "select * from test"
        # data = await TestModel.query(sql)
        # data = await TestModel.add_new(
        #     data={
        #         "name": "test2",
        #         "create_time": "2000-01-01 00:00:00"
        #     }
        # )
        # data = [
        #     {"name": "test3", "create_time": "2000-01-01 00:00:00"},
        #     {"name": "test4", "create_time": "2000-01-01 00:00:00"}
        # ]
        # result = await TestModel.add_multy(data)
        # new_data = {"name": "新的名字"}
        # result = await TestModel.update_conditions(
        #     conditions={"id": 1},
        #     new_values=new_data
        # )
        # new_data = {"name": "新的名字2"}
        # result = await TestModel.update_conditions(
        #     conditions={"id": ['>', 0]},
        #     new_values=new_data
        # )
        # result = await TestModel.get_conditions(
        #     conditions={
        #         "id": 1
        #     },
        #     key="id",
        #     return_fields=['name']
        # )
        # result = await TestModel.get_conditions_page(
        #     conditions={
        #         "id": ['>', 0]
        #     },
        #     page=2,
        #     page_size=2
        # )
        # 测试事务 2个更新事务
        # try:
        #     async with self.db.begin():
        #         result = await TestModel.update_conditions(
        #             conditions={"id": 1},
        #             new_values={"name": "新的名字8"},
        #             is_transaction=True
        #         )
        #         if not result:
        #             raise ValueError("更新失败 第一个表")
        #         result = await Test2Model.update_conditions(
        #             conditions={"id": 1},
        #             new_values={"name": "新的名字3"},
        #             is_transaction=True
        #         )
        #         if not result:
        #             raise ValueError("更新失败 第二个表")
        #         result = "成功"
        # except Exception as e:
        #     print(e)
        #     result = "失败"
        # 测试事务 1个更新 1添加
        # try:
        #     async with self.db.begin():
        #         result = await TestModel.update_conditions(
        #             conditions={"id": 1},
        #             new_values={"name": "新的名字19"},
        #             is_transaction=True
        #         )
        #         if not result:
        #             raise ValueError("更新失败 第一个表")
        #         result = await Test2Model.add_one(
        #             data={
        #                 "name": "新的名字102",
        #                 "create_time": "2000-01-01 00:00:00"
        #             },
        #             is_transaction=True
        #         )
        #         if not result:
        #             raise ValueError("更新失败 第二个表")
        #         result = "成功"
        # except Exception as e:
        #     print(e)
        #     result = "失败"

        return self.r(data=result)

    async def test_get(self):
        """获取方法"""
        data = {
            "area_shop_id": 0,
            "message_type": "00000000",
            "message_content": "测试消息内容",
            "user_id": 1
        }
        new_message = await MessageAction.add_user_message(**data)
        return self.r(code=200, data=new_message)

# -*- coding: utf-8 -*-
"""测试  文件描述"""
from app.controllers.user.base_user_controller import BaseUserController


class TestUserController(BaseUserController):
    """测试用户控制器"""

    async def index(self,):
        """测试方法"""
        data = {
            "area_shop_id": 0,
            "message_type": "00000000",
            "message_content": "测试消息内容",
            "user_id": 1
        }
        new_message = await MessageAction.add_user_message(**data)
        self.r(code=200, data=new_message)

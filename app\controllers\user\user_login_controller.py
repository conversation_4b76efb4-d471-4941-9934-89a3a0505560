# -*- coding: utf-8 -*-
"""用户/注册/登录/退出/"""
import uuid
from app.controllers.user.base_user_controller import BaseUserController
from app.models.user_model import UserRegTempCodeModel, UserRegSendTimesModel, \
    UserLogModel, UserModel
from app.utils.email_function import EmailFunction
from app.utils.function import verify_email, get_datetime, verify_password_level, sha1, get_random_code
from app.utils.verification import Captcha_Obj
from setting import SESSION_SECRET


class UserLoginController(BaseUserController):
    """用户控制器"""

    async def get_captcha(self):
        """获取验证码图片"""
        session_id = self.get_session("session_id")
        if not session_id:
            session_id = str(uuid.uuid4())
            self.set_session("session_id", session_id)
        # 获取 verify_name
        key, code_text, image_base64data = Captcha_Obj.generate_captcha()
        verify_name = self.get_session(session_id+"_verify_name")
        if verify_name:
            # 如果有老的, 删除 老得 名称 和过期时间
            self.pop_session(verify_name)
            self.pop_session(verify_name+"time")
        # 设置新的名称 和过期时间
        self.set_session(session_id+"_verify_name", key, 300)
        self.set_session(key, code_text, 300)
        self.set_session(key+"time", get_datetime("timestamp")+300, 300)
        return self.r(
            code=200,
            data={
                "img_code": image_base64data,
                "key": key,
                "code": code_text
            }
        )

    def _verify_captcha_code(self, key, user_input_code) -> bool:
        """验证验证码"""
        session_id = self.get_session("session_id")
        if not session_id or not key or not user_input_code:
            return False
        # 验证key
        verify_name = self.get_session(session_id+"_verify_name")
        if verify_name != key:
            return False
        # 验证过期时间
        code_time = self.get_session(key+"time")
        if code_time and int(code_time) < get_datetime("timestamp"):
            return False
        code_text = self.get_session(key)
        if not code_text:
            return False
        if code_text == user_input_code:
            # 删除 历史
            self.pop_session(session_id+"_verify_name")
            self.pop_session(key)
            self.pop_session(key+"time")
            return True
        return False

    async def user_sent_email(self):
        """用户获取邮箱验证码"""
        email = (await self.request.json()).get('email', None)
        if not email or not verify_email(email):
            return self.r(
                code=101001,
                msg='Email format is incorrect'
            )
        verify_code, need_img_code = await self._reg_need_verify_img()
        if verify_code:
            # 如果 可以发送邮件
            result = await self._send_reg_email(email)
            print("发送邮件", result)
            code = 200 if result else 101002
        else:
            code = 301001
        data = {"need_verify_img": need_img_code} if isinstance(
            need_img_code, bool) else {}
        if code == 200:

            response = self.r(
                code=200,
                data=data,
                msg='Email sent successfully'
            )
            if not data:
                self.set_session('session_id', str(need_img_code))
            return response
        # 发送失败
        return self.r(
            code=code,
            data=data,
            msg="Email send failed" if code == 101002
                else "Verification code error"  # 301001图片验证码失败
        )

    async def _send_reg_email(self, email):
        """发送注册邮箱"""
        # 查询存在 的code
        code_info = await UserRegTempCodeModel.get_one_conditions(
            conditions={
                "email": email
            }
        )
        if not code_info:
            # 新增
            send_flag, code = EmailFunction.send_verify_code(email)
            if send_flag:
                # 新增成功
                await UserRegTempCodeModel.add_one(
                    data={
                        "email": email,
                        "code": code,
                        "first_time": get_datetime("timestamp"),
                        "send_times": 1
                    }
                )
                return True
            return False
        # 更新
        now_timestamp = get_datetime("timestamp")
        if code_info.get("first_time") + 3600 < now_timestamp and \
                code_info.get("send_times", 0) < 5:
            # 小时超过 5次
            return False

        # 更新
        send_flag, code = EmailFunction.send_verify_code(email)
        if send_flag:
            # 更新成功
            await UserRegTempCodeModel.update_conditions(
                conditions={
                    "id": code_info.get("id")
                },
                new_values={
                    "send_times": code_info.get("send_times", 0) + 1,
                    'first_time': now_timestamp if now_timestamp > code_info.get("first_time") + 3600
                    else code_info.get("first_time")
                }
            )
            return True
        return False

    async def _reg_need_verify_img(self):
        """判断下一次是否有验证图片，以及本次是否验证 图片"""
        session_id = self.get_session('session_id')
        # 验证码成功 ，是否有下一个图片
        if not session_id:
            # 生成session_id
            session_id = str(uuid.uuid4())
            # 如果是首次, 记录到数据库,返回验证成功， 不需要下次
            await UserRegSendTimesModel.add_one(
                data={
                    "session_id": session_id,
                    "times": 1,
                    'last_update_timestamp': get_datetime("timestamp")
                }
            )
            return True, session_id

        # 非数次发送 检查发送次数
        session_send_info = await UserRegSendTimesModel.get_one_conditions(
            conditions={
                "session_id": session_id
            }
        )
        if not session_send_info:
            await UserRegSendTimesModel.add_one(
                data={
                    "session_id": session_id,
                    "times": 1,
                    'last_update_timestamp': get_datetime("timestamp")
                }
            )
            return True, session_id
        if session_send_info.get('times', 0) >= 3:
            # 如果是3次+第四次发送（记录大于已经3次 第四次发送） ，则需要
            key = (await self.request.json()).get('key', None)
            verify_code = (await self.request.json()).get('verify_code', None)
            if not self._verify_captcha_code(key, verify_code):
                return False, True
            # 验证成功，是否需要验证码
            return True, True

        # 更新次数， 小于 3次
        await UserRegSendTimesModel.update_conditions(
            conditions={
                "session_id": session_id
            },
            new_values={
                "times": session_send_info.get('times', 0) + 1,
                'last_update_timestamp': get_datetime("timestamp")
            }
        )
        if session_send_info.get('times', 0) == 2:
            # 如果是第二次
            return True, True
        # 其他 次数
        return True, False

    async def user_register(self):
        """用户注册"""
        data = await self.request.json()
        err_list = []
        email_code = data.get('email_code')
        if not email_code:
            err_list.append("Email code is empty")
        email = data.get('email')
        if not email or not verify_email(email):
            err_list.append("Email format is incorrect")

        password = data.get('password')
        if not password or not verify_password_level(password):
            # 最低8位 需要 数字 字母 和 特殊符号
            err_list.append(
                "Password is empty or not strong enough, need 8 bits, number, letter and special characters")
        re_password = data.get('re_password')
        if not re_password or re_password != password:
            err_list.append("Re-entered password is not consistent")
        # 检查是否注册了
        user_info = await UserLogModel.get_one_conditions(
            conditions={
                "email": email
            }
        )
        if user_info:
            err_list.append("Email has been registered")
        if err_list:
            return self.r(code=300001, msg=",".join(err_list))
        # 判断 验证码 是否正确
        email_code_info = await UserRegTempCodeModel.get_one_conditions(
            conditions={
                "email": email,
                "code": email_code
            }
        )
        if not email_code_info or email_code_info['first_time'] + 300 < get_datetime("timestamp"):
            err_list.append("Email code is incorrect")
        if err_list:
            return self.r(code=300001, msg=",".join(err_list))
        # 增加新的用户
        result = await UserModel.add_one(
            data={
                "nick_name": email.split("@")[0],
                "email": email,
                "google_joint_id": "",
                "password": sha1(password),
                "google_joint_other": "",
                "reg_datetime": get_datetime()
            }
        )
        if result:
            # 注册成功 清理 email 历史code
            await UserRegTempCodeModel.delete_conditions(
                conditions={
                    "email": email
                }
            )
            # 清理 发送次数
            if session_id := self.get_session('session_id'):
                await UserRegSendTimesModel.delete_conditions(
                    conditions={
                        "session_id": session_id
                    }
                )
            auth_token = self.set_login_user(result, True)
            return self.r(code=200, msg="Register success", data={'auth_token': auth_token})
        return self.r(code=300001, msg="Register failed")

    async def user_login(self):
        """用户登录"""
        data = await self.request.json()
        err_list = []
        email = data.get('email')
        if not email or not verify_email(email):
            err_list.append("Email format is incorrect")
        password = data.get('password')
        if not password or not verify_password_level(password):
            # 最低8位 或者 不符合规则
            err_list.append(
                "Password is empty or not strong enough, need 8 bits, number, letter and special characters")
        key = data.get('key', None)
        verify_code = data.get('verify_code', None)
        if not self._verify_captcha_code(key, verify_code):
            err_list.append("Verification code is incorrect")
        if err_list:
            return self.r(code=300001, msg=",".join(err_list))
        # 登录
        user_info = await UserModel.get_one_conditions(
            conditions={
                "email": email,
                "password": sha1(password)
            }
        )
        if not user_info:
            return self.r(code=102001, msg="Login failed, account or password error")
        # 登录成功
        auth_token = self.set_login_user(user_info['id'])
        return self.r(code=200, msg="Login success", data={'auth_token': auth_token})

    async def user_update_password(self):
        """修改密码"""
        user_info = await UserModel.get_one_conditions(
            conditions={
                "id": self.request.state.login_user_id
            }
        )
        if self.request.method == "GET":
            return self.r(code=200,
                          data={"set_password": bool(user_info['password'])},
                          msg="success"
                          )
        # post 更改密码
        post_data = await self.request.json()
        # 验证验证码
        key = post_data.get('key', None)
        verify_code = post_data.get('verify_code', None)
        if not self._verify_captcha_code(key, verify_code):
            return self.r(code=300001, msg="Verification code is incorrect")

        if bool(user_info['password']):
            old_password = post_data.get("old_password")
            if not old_password:
                return self.r(code=300001, msg="Old password is empty")
            if sha1(old_password) != user_info['password']:
                return self.r(code=300001, msg="Old password is incorrect")
        new_password = post_data.get("new_password")
        if not new_password or not verify_password_level(new_password):
            # 最低8位 或者 不符合规则
            return self.r(code=300001, msg="New password is empty or not strong enough, need 8 bits, number, letter and special characters")
        re_password = post_data.get("re_password")
        if not re_password or re_password != new_password:
            return self.r(code=300001, msg="Re-entered password is not consistent")
        # 更新密码
        result = await UserModel.update_conditions(
            conditions={
                "id": self.request.state.login_user_id
            },
            new_values={
                "password": sha1(new_password)
            }
        )
        if result:
            return self.r(code=200, msg="success")

        return self.r(code=102003, msg="Update password failed")

    async def user_logout(self):
        """用户退出登录"""
        self.clear_login_user()
        return self.r(code=200, msg="Logout success")

    async def forget_password_send_email(self):
        """忘记密码"""
        post_data = await self.request.json()
        email = post_data.get('email')
        if not email or not verify_email(email):
            return self.r(code=300001, msg="Email format is incorrect")
        # 检查是否注册了
        user_info = await UserModel.get_one_conditions(
            conditions={
                "email": email
            }
        )

        if not user_info:
            return self.r(code=102004, msg="Email has not been registered")
        # 生成加密码
        base_code = f"{user_info['id']}{email}{get_datetime('timestamp')}{SESSION_SECRET}"
        code = sha1(base_code + get_random_code())
        timeout = get_datetime("timestamp") + 72*3600
        # 更新数据库
        result = await UserModel.update_conditions(
            conditions={
                "id": user_info['id']
            },
            new_values={
                "reset_password_url_code": code,
                "reset_password_url_timeout": timeout
            }
        )
        if result:
            # 发送邮件
            email_result = EmailFunction.send_reset_password(
                email=email,
                random_str=code
            )
            if email_result:
                return self.r(code=200, msg="success")
            return self.r(code=300002, msg="Send email failed")
        # 系统请求失败，请联系管理员
        return self.r(code=102005, msg="System error,please connect system manager!")

    async def reset_password_by_email_code(self):
        """通过邮箱重置密码"""

        if self.request.method == "GET":
            random_str = self.request.query_params.get('random_str')
        else:
            post_data = await self.request.json()
            random_str = post_data.get('random_str')

        if not random_str or len(random_str) != 64:
            return self.r(code=300001, msg="Random str is empty")
        user_info = await UserModel.get_one_conditions(
            conditions={
                "reset_password_url_code": random_str
            }
        )
        if not user_info or user_info['reset_password_url_timeout'] < get_datetime("timestamp"):
            # 链接已经失效
            return self.r(code="102006", msg="Reset password link has expired")

        if self.request.method == "GET":
            # 链接有效
            return self.r(code=200, msg="Reset password link is valid")
        # 更新密码
        password = post_data.get('new_password')
        re_password = post_data.get('re_passsword')
        # 判断格式以及重复性
        if not password or not verify_password_level(password):
            # 最低8位 需要 数字 字母 和 特殊符号
            self.r(code=300001, msg="Password is empty or not strong enough, need 8 bits, number, letter and special characters")
        re_password = post_data.get('re_password')
        if not re_password or re_password != password:
            self.r(code=300001, msg="Re-entered password is not consistent")
        # 更新密码
        result = await UserModel.update_conditions(
            conditions={
                "id": user_info['id']
            },
            new_values={
                "password": sha1(password),
                "reset_password_url_code": "",
                "reset_password_url_timeout": 0
            }
        )
        if result:
            return self.r(code=200, msg='Success')
        # 更新密码失败
        return self.r(code=102005, msg="Reset password faild~")

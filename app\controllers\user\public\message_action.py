# -*- coding: utf-8 -*-
"""定义 用户消息，公共操作类,同样基于用户"""
from sqlalchemy.exc import SQLAlchemyError
from app.models.user_message_model import UserMessageModel, UserMessageRelationModel
# 国家信息

from app.utils.function import get_datetime
from app.utils.const import USER_SHOP_COUNTRY_ROLE


class MessageAction:
    """用户消息公共操作类"""

    @classmethod
    async def get_not_read_msg_count(cls, user_id):
        """获取未读消息数量"""
        if not user_id:
            return 0
        return await UserMessageRelationModel.get_conditions_count(
            conditions={
                "user_id": user_id,
                "is_read": 0,
            }
        )

    @classmethod
    async def add_user_messages_batch(cls, messages_list):
        """批量添加用户消息"""
        if not messages_list:
            return False

        db = UserMessageModel.db()
        try:
            async with db.begin():
                message_ids = []
                for kwargs in messages_list:
                    if not kwargs.get("message_content", None) or not kwargs.get("user_id"):
                        continue

                    message = {
                        "message_title": kwargs.get("message_title", "New message!"),
                        "area_shop_id": kwargs.get("area_shop_id", 0),
                        "message_datetime": get_datetime(),
                        "message_type": kwargs.get("message_type", "FFFFFFFF"),
                        "message_content": kwargs.get("message_content"),
                    }

                    message_id = await UserMessageModel.add_one(
                        data=message,
                        is_transaction=True
                    )
                    if not message_id:
                        raise ValueError("添加消息失败")

                    # 添加关系
                    relation = {
                        "user_id": kwargs.get("user_id"),
                        "message_id": message_id,
                        "is_read": 0,
                    }
                    relation_id = await UserMessageRelationModel.add_one(
                        data=relation,
                        is_transaction=True
                    )
                    if not relation_id:
                        raise ValueError("添加关系失败")

                    message_ids.append(message_id)

                await db.commit()
                return message_ids
        except SQLAlchemyError as e:
            print(f"批量添加消息失败: {e}")
            await db.rollback()
            return False

    @classmethod
    async def add_user_message(cls, **kwargs):
        """添加用户消息
        # 单条 用户信息， 指定用户 指定信息
        """

        if not kwargs.get("message_content", None) or not kwargs.get("user_id"):
            return False
        message = {
            "message_title": kwargs.get("message_title", "New message!"),
            "area_shop_id": kwargs.get("area_shop_id", 0),
            "message_datetime": get_datetime(),
            "message_type": kwargs.get("message_type", "FFFFFFFF"),
            "message_content": kwargs.get("message_content"),
        }
        try:
            message_id = await UserMessageModel.add_one(
                data=message
            )
            if not message_id:
                raise ValueError("添加消息失败")
            # 添加关系
            relation = {
                "user_id": kwargs.get("user_id"),
                "message_id": message_id,
                "is_read": 0,
            }
            relation_id = await UserMessageRelationModel.add_one(
                data=relation,
            )
            if not relation_id:
                raise ValueError("添加关系失败")
            return message_id
        except SQLAlchemyError as e:
            print(e)
            return False

    @classmethod
    async def add_country_message(cls, **kwargs):
        """获取用户消息分页
        # 添加基于某个国家的信息，发给不同关系的人
        """
        shop_country_id = kwargs.get("shop_country_id", 0)
        if not shop_country_id:
            return False
        # 获取国家信息 以及关联 用户

    @classmethod
    async def get_user_message_page(cls, **kwargs):
        """获取用户消息分页
        # 获取用户消息分页
        """

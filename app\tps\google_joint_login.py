# -*- coding: utf-8 -*-
"""谷歌联合登录"""
import json
from urllib.parse import urlencode
import requests
from setting import GOOGLE_JOINT_ID, GOOGLE_JOINT_SECRET, GOOGLE_JOINT_REDIRECT_URL


class GoogleJointLogin:
    """谷歌联合登录"""

    # Google OAuth2 相关URL
    GOOGLE_AUTH_URL = "https://accounts.google.com/o/oauth2/auth"
    GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"
    GOOGLE_USERINFO_URL = "https://www.googleapis.com/oauth2/v2/userinfo"

    @classmethod
    def get_jump_url(cls,  state=None):
        """获取联合登录 跳转URL

        Args:
            redirect_uri (str): 回调地址
            state (str, optional): 状态参数，用于防止CSRF攻击

        Returns:
            str: Google OAuth2 授权URL
        """
        if not GOOGLE_JOINT_ID or not GOOGLE_JOINT_REDIRECT_URL:
            # 未配置GOOGLE_JOINT_ID 或 GOOGLE_JOINT_REDIRECT_URL
            return False, "GOOGLE JOINT ID or GOOGLE JOINT REDIRECT URL is not configured"

        params = {
            'client_id': GOOGLE_JOINT_ID,
            'redirect_uri': GOOGLE_JOINT_REDIRECT_URL,
            'scope': 'openid email profile',
            'response_type': 'code',
            'access_type': 'offline',
            'prompt': 'consent'
        }

        if state:
            params['state'] = state

        auth_url = f"{cls.GOOGLE_AUTH_URL}?{urlencode(params)}"
        return True, auth_url

    @classmethod
    def _get_access_token(cls, code, redirect_uri):
        """获取 google access_token"""
        token_data = {
            'client_id': GOOGLE_JOINT_ID,
            'client_secret': GOOGLE_JOINT_SECRET,
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': redirect_uri
        }

        token_response = requests.post(
            cls.GOOGLE_TOKEN_URL,
            data=token_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=30
        )

        if token_response.status_code != 200:
            return {
                'status': False,
                'error': 'Failed to get access token',
                'details': token_response.text
            }

        token_info = token_response.json()
        access_token = token_info.get('access_token')

        if not access_token:
            return {
                'status': False,
                'error': 'No access token received',
                'details': token_info
            }
        return {
            'status': True,
            'access_token': access_token
        }

    @classmethod
    def get_google_userinfo(cls, code):
        """获取google 登录用户信息

        Args:
            code (str): 授权码
            redirect_uri (str): 回调地址

        Returns:
            dict: 用户信息字典，包含用户的基本信息
            {
                "code": 200,
                "data": {
                    "status": true,
                    "user_info": {
                        "id": "123123123",
                        "email": "<EMAIL>",
                        "name": "卢会杰",
                        "given_name": "会杰",
                        "family_name": "卢",
                        "picture": "https://lh3.goo7EB-elcKKDgA1TLcEuZ1lvqwW8Rt6kATm6vd7v7A=s96-c",
                        "locale": null,
                        "verified_email": true
                        }
                    },
                "msg": "success"
            }
        """
        return {
            "code": 200,
            "data": {
                "status": True,
                "user_info": {
                    "id": "123123123",
                    "email": "<EMAIL>",
                    "name": "卢会杰",
                    "given_name": "会杰",
                    "family_name": "卢",
                    "picture": "https://lh3.goo7EB-elcKKDgA1TLcEuZ1lvqwW8Rt6kATm6vd7v7A=s96-c",
                    "locale": None,
                    "verified_email": True
                }
            },
            "msg": "success"
        }
        try:
            # 第一步：使用授权码获取访问令牌
            access_token = cls._get_access_token(
                code, GOOGLE_JOINT_REDIRECT_URL)
            if access_token['status']:
                access_token = access_token['access_token']
            else:
                return {
                    "status": False,
                    "error": access_token.get('error'),
                    "details": access_token.get('details')
                }
            # 第二步：使用访问令牌获取用户信息
            headers = {
                'Authorization': f'Bearer {access_token}'
            }

            userinfo_response = requests.get(
                cls.GOOGLE_USERINFO_URL,
                headers=headers,
                timeout=30
            )

            if userinfo_response.status_code != 200:
                return {
                    'status': False,
                    'error': 'Failed to get user info',
                    'details': userinfo_response.text
                }

            user_info = userinfo_response.json()
            # 返回标准化的用户信息
            return {
                'status': True,
                'user_info': {
                    'id': user_info.get('id'),
                    'email': user_info.get('email'),
                    'name': user_info.get('name'),
                    'given_name': user_info.get('given_name'),
                    'family_name': user_info.get('family_name'),
                    'picture': user_info.get('picture'),
                    'locale': user_info.get('locale'),
                    'verified_email': user_info.get('verified_email', False)
                }
            }

        except requests.RequestException as e:
            return {
                'status': False,
                'error': 'Network error',
                'details': str(e)
            }
        except json.JSONDecodeError as e:
            return {
                'status': False,
                'error': 'JSON decode error',
                'details': str(e)
            }
        except Exception as e:
            return {
                'status': False,
                'error': 'Unexpected error',
                'details': str(e)
            }

# -*- coding: utf-8 -*-
"""用户操作基础类"""
import json
from app.controllers.base_controller import BaseController
from app.utils.function import get_random_code, sha1
from app.models.user_model import UserModel
from app.models.amazon_shop_model import UserVirtualShopCountryRelationModel


class BaseUserController(BaseController):
    """用户操作基础类 继承基础类"""

    def set_login_user(self, user_id, is_new=False):
        """设置登录用户"""
        login_type = 'user'
        # 生成 随机 token 字符串 64位
        base_code = f"{login_type}_{user_id}_{get_random_code()}"
        token = sha1(base_code)
        # 保存 token 到 redis
        self.set_session(login_type+"_"+token, user_id)
        # shops_info = {} if is_new else self.get_user_shops_info(user_id)
        # self.set_session(f"{user_id}_shops", json.dumps(shops_info))
        return token

    async def now_shop_info(self, user_id, v_shop_id=None, all_shop=None):
        """当前店铺信息"""
        if not user_id:
            return None
        user_id = str(user_id)
        key = str(user_id) + "_now_shop"
        if v_shop_id:
            # 如果是直接设置
            all_s_info = await self.get_user_shops_info(user_id) if not all_shop else all_shop
            if all_s_info:
                a_s_info = all_s_info['shops'][str(v_shop_id)]
                self.set_session(key, json.dumps(a_s_info))
                return a_s_info
            return None

        a_s_info = self.get_session(key)
        if a_s_info:
            a_s_info = json.loads(a_s_info)
            return a_s_info
        # 如果没有店铺信息 获取店铺第一个 ，并设置默认
        all_s_info = await self.get_user_shops_info(user_id) if not all_shop else all_shop
        if all_s_info:
            a_s_info = all_s_info['shops'][list(all_s_info['shops'].keys())[0]]
            self.set_session(key, json.dumps(a_s_info))
            return a_s_info
        return None

    def clear_login_user(self,):
        """清除登录用户"""
        user_id = self.request.state.login_user_id
        token = self.request.state.token
        if user_id:
            self.pop_session(f"{user_id}_token")
            # self.pop_session(f"{user_id}_shops")
        if token:
            self.pop_session('user_'+token)

    def get_login_user(self):
        """获取登录用户"""
        return self.get_session("auth_token")

    async def get_user_shops_info(self, user_id):
        """获取用户店铺信息"""
        all_shop = await UserVirtualShopCountryRelationModel.get_user_shop_list(user_id)
        # 循环判断店铺信息
        result = {"shops": {}, "v_shop_area_id_relation": {}}
        for shop_info in all_shop:
            v_shop_id = shop_info['virtual_shop_id']
            if str(v_shop_id) not in result['shops']:
                result['shops'][str(v_shop_id)] = {
                    "shop_name": shop_info['shop_name'],
                    "v_shop_id": v_shop_id,
                    "virtual_shop_vendor": shop_info['virtual_shop_vendor'],
                    "area": {}
                }
            # area
            sp_area_code = shop_info['sp_area_code']
            if sp_area_code not in result['shops'][str(v_shop_id)]['area']:
                result['shops'][str(v_shop_id)]['area'][sp_area_code] = {}
            result['shops'][str(
                v_shop_id)]['area'][sp_area_code][shop_info['country_code']] = shop_info
            shop_area_id_relation = str(
                shop_info['virtual_shop_id']) + "_" + str(shop_info['sp_area_code'])
            if shop_area_id_relation not in result['v_shop_area_id_relation']:
                result['v_shop_area_id_relation'][shop_area_id_relation] = shop_info['sp_info_id']

        return result

    async def get_user_info(self, email=None, user_id=None):
        """同游邮箱或者用户id 获取用户信息"""
        if email:
            where = {"email": email}
        elif user_id:
            where = {"id": user_id}
        else:
            return None
        return await UserModel.get_one_conditions(
            conditions=where
        )

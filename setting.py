# -*- coding: utf-8 -*-
"""加载配置文件"""
import os
from urllib.parse import quote_plus
from dotenv import load_dotenv
from app.config.lang import SYS_LANG_CODE

load_dotenv(override=True)
ROOT = os.path.dirname(__file__)  # 只需要一次dirname
# debug model
DEBUG = os.getenv("DEBUG", "false") == "true"
# serverr port
SERVER_PORT = int(os.getenv("SERVER_PORT", "9000"))
# mysql setting
DATABASE_HOST = os.getenv("DATABASE_HOST", "127.0.0.1")
DATABASE_USER = quote_plus(os.getenv("DATABASE_USER", "root"))
DATABASE_PORT = int(os.getenv("DATABASE_PORT", "3306"))
DATABASE_PSWD = quote_plus(os.getenv("DATABASE_PSWD", ""))
DATABASE_NAME = os.getenv("DATABASE_NAME", "")
TABLE_PREFIX = os.getenv("TABLE_PREFIX", "atlas_")
# URI 前缀
URI_PREFIX = os.getenv("URI_PREFIX", "").strip("/")
SESSION_SECRET = os.getenv("SESSION_SECRET", "atlasisverygood666")
SESSION_EXPIRE = int(os.getenv("SESSION_EXPIRE", "3600"))

# redis 配置
REDIS_HOST = os.getenv("REDIS_HOST", "127.0.0.1")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_PSWD = os.getenv("REDIS_PSWD", "")
REDIS_DB = int(os.getenv("REDIS_DB", "0"))

# 其他配置
LANG = os.getenv("LANG", "EN")
# 语言包
SYS_LANG = SYS_LANG_CODE[LANG]
# 谷歌联合登录

GOOGLE_JOINT_ID = os.getenv("GOOGLE_JOINT_ID", "")
GOOGLE_JOINT_SECRET = os.getenv("GOOGLE_JOINT_SECRET", "")
GOOGLE_JOINT_REDIRECT_URL = os.getenv("GOOGLE_JOINT_REDIRECT_URL", "")

# LOG DIR
LOG_DIR = os.path.join(ROOT, "log")
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

# 邮箱配置
MAIL_HOST_SERVER = os.getenv("MAIL_HOST_SERVER", "")
MAIL_PORT = os.getenv("MAIL_PORT", "")
MAIL_USERNAME = os.getenv("MAIL_USERNAME", "")
MAIL_PASSWORD = os.getenv("MAIL_PASSWORD", "")
MAIL_ENCRYPTION = os.getenv("MAIL_ENCRYPTION", "")
MAIL_FROM_ADDRESS = os.getenv("MAIL_FROM_ADDRESS", "")

# 亚马逊应用相关
AMAZON_SP_APPLICATION_ID = os.getenv("AMAZON_SP_APPLICATION_ID", "")
AMAZON_SP_PARTNER_ID = os.getenv("AMAZON_SP_PARTNER_ID", "")
AMAZON_SP_SECRET = os.getenv("AMAZON_SP_SECRET", "")
AMAZON_SP_RETURN_URL = os.getenv("AMAZON_SP_RETURN_URL", "")
# 亚马逊广告设置
AMAZON_AD_PARTNER_ID = os.getenv("AMAZON_AD_PARTNER_ID", "")
AMAZON_AD_SECRET = os.getenv("AMAZON_AD_SECRET", "")
AMAZON_AD_RETURN_URL = os.getenv("AMAZON_AD_RETURN_URL", "")
# 获取token 带来的代理
ACCESS_TOKEN_DAILI_URL = os.getenv("ACCESS_TOKEN_DAILI_URL", "")
BASE_DOMAIN = os.getenv("BASE_DOMAIN", "http://127.0.0.1:9001")
# 数据接口验证
DATA_API_TOKEN_SECRET = os.getenv("DATA_API_TOKEN_SECRET", "")

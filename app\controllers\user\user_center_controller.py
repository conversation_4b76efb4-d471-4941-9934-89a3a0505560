# -*- coding: utf-8 -*-
"""python  用户中心"""
from app.controllers.user.base_user_controller import BaseUserController
from app.controllers.user.public.message_action import MessageAction


class UserCenterController(BaseUserController):
    """用户中心控制器"""

    async def index(self):
        """用户中心首页"""
        user_id = self.request.state.login_user_id
        # 获取未读消息
        result = {}
        result['not_read_msg_count'] = await MessageAction.get_not_read_msg_count(user_id)
        # 获取当前激活店铺信息
        result['all_shop'] = await self.get_user_shops_info(user_id)
        result['now_shop'] = await self.now_shop_info(user_id=user_id, all_shop=result['all_shop'])

        return self.r(data=result)

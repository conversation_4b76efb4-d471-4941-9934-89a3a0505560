# -*- coding: utf-8 -*-
"""定义路由器"""
# Introduce the controller
from app.controllers.test.index import Test<PERSON>ontroller
# 用户注册相关
from app.controllers.user.test_user_controller import TestUserController
from app.controllers.user.user_login_controller import User<PERSON>ogin<PERSON>ontroller
from app.controllers.user.google_login_cotroller import GoogleLoginController
# 店铺相关
from app.controllers.user.user_shop_controller import UserShopController
from app.controllers.user.user_shop_amazon_callback_controller import \
    UserShopAmazonCallBackController
from app.controllers.user.user_shop_right_contrller import UserShopRightController
from app.controllers.user.user_center_controller import UserCenterController

# define router
ROUTER = [
    {
        "path": "/",  # 测试用
        # "method": ["GET"], 默认 POST
        "action": TestController.index,
        "verify": "user"  # 默认 False
    },
    {
        "path": "/secure",  # 测试用
        "method": "GET",  # 默认 POST
        "action": TestController.test_get,
        "verify": False
    }, {
        "path": "/test_user",  # 测试用
        "action": TestUserController.index,
        "verify": False
    },
    {  # 谷歌联合登录
        "path": "/google_jump_url",
        "action": GoogleLoginController.get_google_jump_url,
        "verify": False
    },
    {  # 谷歌回调 获取用户信息
        "path": "/google_jump_url_login",
        "action": GoogleLoginController.google_login,
        "verify": False
    },
    {  # 发送邮箱
        "path": "/user_sent_email",
        "action": UserLoginController.user_sent_email,
        "verify": False
    }, {  # 获取图形验证码
        "path": "/captcha",
        "action": UserLoginController.get_captcha,
        "verify": False
    }, {  # 注册
        "path": "/user_register",
        "action": UserLoginController.user_register,
        "verify": False
    }, {  # 登录
        "path": "/user_login",
        "action": UserLoginController.user_login,
        "verify": False
    }, {  # 忘记密码发送邮件
        "path": "/user_forget_password_send_email",
        "action": UserLoginController.forget_password_send_email,
        "verify": False
    }, {  # 通过邮箱url 重置密码
        "path": "/user_rest_password_by_email_code",
        "method": ['GET', 'POST'],
        "action": UserLoginController.reset_password_by_email_code,
        "verify": False
    }    # -----------------用户登录后操作------------------
    , {  # 修改密码
        "path": "/user_update_password",
        "method": ["POST", "GET"],
        "action": UserLoginController.user_update_password,
        "verify": 'user'
    }, {  # 退出登录
        "path": "/user_logout",
        "method": "GET",
        "action": UserLoginController.user_logout,
        "verify": 'user'
    }, {  # 用户添加虚拟店铺 - SP授权地址
        "path": "/user_add_virtual_sp_shop",
        "action": UserShopController.add_user_virtual_shop,
        "verify": 'user'
    }, {  # 用户已经有的虚拟店铺列表以及国家
        "path": "/user_virtual_shop_list",
        "action": UserShopController.get_user_virtual_shop,
        "verify": 'user'
    }, {  # 亚马逊授权SP 回调
        "path": "/user_amazon_sp_callback",
        "action": UserShopAmazonCallBackController.amazon_sp_callback,
        "verify": 'user'
    }, {  # 亚马逊授权AD 跳转url
        "path": "/user_add_ad_shop",
        "action": UserShopController.get_ad_amazon_auth,
        "verify": 'user'
    }, {  # 亚马逊授权AD 回调
        "path": "/user_amazon_ad_callback",
        "action": UserShopAmazonCallBackController.amazon_ad_callback,
        "verify": 'user'
    }, {  # 邀请发送邮件
        "path": "/user_invate_send_email",
        "action": UserShopRightController.user_invate_send_email,
        "verify": {'type': 'user'}  # dict 表示可以是非登录状态
    }, {  # 定义 验证发送邮件
        "path": "/user_invate_verify_email_code",
        "action": UserShopRightController.verify_email_invate_code,
        "verify": {'type': 'user'}  # dict 表示可以是非登录状态
    }, {  # 登录后用户中心- 店铺数据等-非个人业务中心
        "path": "/user_center",
        "action": UserCenterController.index,
        "verify": "user"
    }, {  # 用户个人权限
        "path": "/get_my_shop_right",
        "action": UserShopRightController.get_my_shop_right,
        "verify": "user"
    }, {  # 用户权限申请
        "path": "/apply_shop_right",
        "action": UserShopRightController.apply_shop_right,
        "verify": "user"
    }, {  # 用户撤销申请
        "path": "/withdraw_shop_right",
        "action": UserShopRightController.withdraw_shop_right,
        "verify": "user"
    }, {  # 处理权限-- 店铺的管理员
        "path": "/process_shop_right",
        "action": UserShopRightController.process_shop_right,
        "verify": "user"
    }

]

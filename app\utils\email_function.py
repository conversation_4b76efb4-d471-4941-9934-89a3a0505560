# -*- coding: utf-8 -*-
"""邮箱功能类"""

import random
from app.utils.function import get_datetime
from app.tps.email_sender import send_email

from app.utils.const import USER_SHOP_COUNTRY_ROLE
from setting import BASE_DOMAIN


class EmailFunction:
    """邮箱功能类"""

    @classmethod
    def setn_invate_email(cls, from_user_name, email, invate_code, shop_name, shop_country, country_role):
        """ 发送邀请 吗"""
        country_role = str(country_role)
        subject = f"【DeepBI】You’ve been invited to join [{shop_name}] ([{shop_country}]) as a [{USER_SHOP_COUNTRY_ROLE[country_role]}]"

        body = f"""
            {from_user_name} has invited you to join the DeepBI advertising team for 【{shop_name}】 ({shop_country}) as a {USER_SHOP_COUNTRY_ROLE[country_role]}.
            <br>
            👉 Click the button below to register and join the team. This link is valid for 3 days and can only be used by the invited email address:
            <br>
            <a href="{BASE_DOMAIN}/register?invite_code={invate_code}">[Join the Team]</a>
            <br>
            <br>
            If you are not the intended recipient, please ignore this email.<br>
            Access to the site requires approval by the site owner after registration.<br>
            Thank you for choosing DeepBI!
            <br>
            <br>
            <em>This email was automatically sent by the DeepBI advertising system. Please do not reply directly.</em>
            <br>
            <em>If you are not the intended recipient, please disregard this message or contact our support team.</em>
            <br>
            🔗 DeepBI Website: https://www.deepbi.cn 
            <br>
            📬 Support: <EMAIL>
        """
        result, msg = send_email(email, subject, body)
        if result:
            return True
        print(msg)
        return False

    @classmethod
    def send_verify_code(cls, email, random_str=None):
        """生成并发送验证码 - 入口函数"""
        if random_str is None:
            random_int = random.randint(100000, 999999)
            random_str = f"{random_int:06d}"
        subject = "Atlas 验证码"
        body = f"您的验证码是：{random_str} 请在10分钟内完成验证<br> Atlas 团队 <br> {get_datetime()}"
        result, msg = send_email(email, subject, body)
        if result:
            return True, random_str
        print(msg)
        return False, None

    @classmethod
    def send_reset_password(cls, email,  random_str=None, user_name=None,):
        """发送重置密码邮件 - 入口函数"""
        if not email or not random_str:
            return False

        if not user_name:
            user_name = email.split("@")[0]
        forget_link = f"{random_str}"
        subject = "Atlas password recovery"
        body = f"""
            <style>
            table,tr,td {{
                border: none;
                width: 85%;
                text-align: left:
                smargin: 0 auto;
                padding:15px;
                font-size:24px;
                color: #000;
            }}
            </style>
            <table>
                <tr>
                    <td style="text-align:center;border-bottom: 3px solid #2563eb;">
                        <img src="https://www.deepbi.cn/images/Header/deepbilogo.png" height="70px" width="200px">
                    </td>
                </tr>
                <tr>
                    <td>
                        Hello <b>{user_name}</b>
                    </td>
                </tr>
                <tr><td>Fllow this link to reset your password: </td></tr>
                <tr><td><a href="{forget_link}">{forget_link}</a></td></tr>
                <tr>
                    <td>
                    <em>We look forwad to seeing you rise to the top with Atlas.</em>
                    </td>
                </tr>
                <tr>
                    <td style="border-bottom: 3px solid #2563eb;">
                     Sincerely, <br>
                     Atlas Team<br>
                    {get_datetime()}
                    </td>
                </tr> 
            </table>
        """
        result, msg = send_email(email, subject, body)
        if result:
            return True
        print(msg)
        return False

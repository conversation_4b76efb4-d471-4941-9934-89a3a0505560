# -*- coding: utf-8 -*-
"""web 服务"""
import sys
from fastapi import FastAPI, Depends, Request, HTTPException
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.sessions import SessionMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from app.verify_function import verify_token
from app.config.router import ROUTER
from app.config.data_router import DATA_ROUTER
from app.db import get_db
from app.utils.redis_function import RedisFunction, get_redis
from setting import URI_PREFIX, SESSION_SECRET, SESSION_EXPIRE


def create_app():
    """Create fastapi application object"""
    path = []
    app = FastAPI()

    @app.exception_handler(HTTPException)
    async def custom_http_exception_handler(request: Request, exc: HTTPException):
        """自定义HTTP异常处理器"""
        if isinstance(exc.detail, dict) and "code" in exc.detail and "msg" in exc.detail:
            return JSONResponse(
                status_code=exc.status_code,
                content=exc.detail  # 直接返回 {"code": xxx, "msg": "xxx"}
            )
        # 如果是字符串格式，转换为自定义格式
        return JSONResponse(
            status_code=exc.status_code,
            content={"code": 40000, "msg": str(exc.detail)}
        )

    def create_verify_dependency(route_config):
        """创建自定义的verify_token依赖 传递route参数"""
        async def custom_verify_token(
            request: Request,
            redis: RedisFunction = Depends(get_redis)
        ):
            verify_config = route_config.get("verify", {})
            return await verify_token(request, redis, verify_config)

        return custom_verify_token
    all_router = DATA_ROUTER + ROUTER
    for route in all_router:
        action_func = route.get('action', None)
        if not action_func:
            continue
        verify = [] if not route.get("verify", True) else [
            Depends(create_verify_dependency(route))]
        route_path = route['path']
        if route_path not in path:
            path.append(route_path)
        else:
            raise ValueError(f"路由重复定义：{route_path}")
        # 组合 URI_PREFIX
        if URI_PREFIX:
            route_path = f"/{URI_PREFIX}{route_path}"
        cls_name = action_func.__qualname__.split('.', maxsplit=1)[0]
        module = sys.modules[action_func.__module__]
        cls = getattr(module, cls_name)
        if not cls:
            raise ValueError(f"{route_path}控制器类 {cls_name} 不存在")
        action = action_func.__name__
        if not action:
            raise ValueError(f"{route_path}方法 {action} 不存在")

        def make_endpoint(cls, action):
            async def endpoint(
                request: Request,
                db: AsyncSession = Depends(get_db),
                redis: RedisFunction = Depends(get_redis)
            ):
                controller = cls(request, db, redis)  # 显式注入
                return await controller.dispatch(getattr(controller, action))
            return endpoint

        method = route.get('method', 'POST')
        if isinstance(method, str):
            method = [method]

        app.add_api_route(
            path=route_path,
            endpoint=make_endpoint(cls, action),
            methods=method,
            dependencies=verify
        )
        app.add_middleware(
            SessionMiddleware,
            secret_key=SESSION_SECRET,
            max_age=SESSION_EXPIRE
        )
    return app

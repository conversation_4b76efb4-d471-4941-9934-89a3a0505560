# -*- coding: utf-8 -*-
"""谷歌用户联合登录"""
import json
from app.controllers.user.base_user_controller import BaseUserController
from app.tps.google_joint_login import GoogleJointLogin
from app.models.user_model import UserModel
from app.utils.function import get_datetime


class GoogleLoginController(BaseUserController):
    """谷歌用户联合登录"""

    async def get_google_jump_url(self):
        """获取跳转地址"""
        result, jump_url = GoogleJointLogin.get_jump_url()
        if result:
            return self.r(code=200, msg="success", data={"jump_url": jump_url})

        return self.r(code=300001, msg="get jump url failed")

    async def google_login(self):
        """谷歌登录"""
        code = (await self.request.json()).get('code', '')
        if not code:
            return self.r(code=300001, msg="Args code is required")

        result = GoogleJointLogin.get_google_userinfo(
            code)
        if result['code'] == 200 and result['data']['status']:
            # 登录成功
            google_user_info = result['data']['user_info']
            # 注册用户
            google_joint_id = google_user_info['id']
            verified_email = google_user_info['verified_email']
            if not verified_email:
                # google 邮箱未验证,
                return self.r(
                    code=103001,
                    msg="Registration failed, This Google account is not verified. "
                        "Please recover your google account"
                )
            # 判断是否是新的 账号
            user_info = await UserModel.get_one_conditions(
                conditions={
                    "google_joint_id": google_joint_id
                }
            )
            if user_info:
                # 如果是老账号
                result = {
                    "is_new": False,
                    "is_set_password": bool(user_info['password']),
                    "auth_token": self.set_login_user(uid=user_info['id'])
                }
            else:
                # 如果是新账号
                google_joint_other = json.dumps(google_user_info)
                # 注册用户
                user_id = await UserModel.add_one(
                    data={
                        "nick_name": google_user_info['name'],
                        "email": google_user_info['email'],
                        "google_joint_id": google_joint_id,
                        "google_joint_other": google_joint_other,
                        "reg_datetime": get_datetime()
                    }
                )
                result = {
                    "is_new": True,
                    "is_set_password": False,
                    "auth_token": self.set_login_user(uid=user_id)
                }
            return self.r(code=200, data=result)
        # 联合登录失败
        return self.r(code=103002, data=result)

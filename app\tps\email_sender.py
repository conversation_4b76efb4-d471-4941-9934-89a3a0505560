# -*- coding: utf-8 -*-
"""发送邮件处理"""
import os
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from setting import MAIL_HOST_SERVER, MAIL_PORT, MAIL_PASSWORD, MAIL_FROM_ADDRESS, MAIL_USERNAME


def send_email(to_addrs, subject, body, attachment_paths=None):
    """send email """
    try:
        if isinstance(to_addrs, str):
            to_addrs = to_addrs.strip(",").split(",")

        # 设置 MIME 多部分消息
        message = MIMEMultipart()
        message['From'] = MAIL_FROM_ADDRESS
        message['To'] = ", ".join(to_addrs)
        message['Subject'] = subject

        # 添加邮件正文
        message.attach(MIMEText(body, 'html'))

        # 添加附件
        if attachment_paths:
            for file_path in attachment_paths:
                if os.path.exists(file_path):
                    part = MIMEBase('application', 'octet-stream')
                    with open(file_path, 'rb') as attachment:
                        part.set_payload(attachment.read())
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename={os.path.basename(file_path)}'
                    )
                    message.attach(part)

        # 连接 SMTP 服务器并发送邮件
        with smtplib.SMTP_SSL(MAIL_HOST_SERVER, MAIL_PORT) as server:
            server.login(MAIL_USERNAME, MAIL_PASSWORD)
            server.sendmail(MAIL_FROM_ADDRESS, to_addrs,
                            message.as_string())
        return True, "Success"
    except Exception as e:
        return False, str(e)


# 使用示例
if __name__ == "__main__":
    send_email(
        to_addrs=[""],
        subject="title",
        body="contnets",
        # attachment_paths=["./uploads/1.pdf"]
    )

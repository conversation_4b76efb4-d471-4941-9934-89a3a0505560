# -*- coding: utf-8 -*-
"""基础控制器"""
from fastapi import Depends, Request
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from app.db import get_db, bind_db
from app.utils.redis_function import RedisFunction, get_redis
from setting import SESSION_EXPIRE


class BaseController:
    """basic controller"""

    def __init__(self,
                 request: Request,
                 db: AsyncSession = Depends(get_db),
                 redis: RedisFunction = Depends(get_redis)
                 ):
        """init bind db"""
        self.request = request
        self.db = db
        self.redis = redis
        bind_db(db)  # 自动绑定 db

    async def init_user(self, token: str = None):
        """init user"""
        if token:
            return None
        return None

    async def dispatch(self, method, *args, **kwargs):
        """可以统一封装 dispatch() 调度入口"""
        return await method(*args, **kwargs)

    def r(self, code=200, data=None, msg="success"):
        """response data"""
        if not data:
            data = {}
        if not msg:
            msg = ''

        data = {
            'code': int(code),
            'data': data,
            'msg': str(msg)
        }
        return JSONResponse(content=data, status_code=200)

    def set_session(self, key: str, value: str, expire: int = SESSION_EXPIRE):
        """设置session"""
        if self.redis is not None:
            return self.redis.set(key, value, expire)
        self.request.session[key] = value
        return True

    def get_session(self, key: str):
        """获取session"""
        if self.redis is not None:
            return self.redis.get(key)
        return self.request.session.get(key, None)

    def pop_session(self, key: str):
        """删除session"""
        if self.redis is not None:
            return self.redis.delete(key)
        self.request.session.pop(key, None)
        return True

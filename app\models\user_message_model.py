# -*- coding: utf-8 -*-
"""用户 消息表 """
from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy import and_, func,  asc, desc, select
from sqlalchemy.exc import SQLAlchemyError
from app.base_model import BaseModel


class UserMessageModel(BaseModel):
    """用户消息模型"""

    __tablename__ = BaseModel.table_prefix + "user_message"

    id = Column(Integer, primary_key=True, autoincrement=True)
    area_shop_id = Column(Integer, nullable=False, default=0, comment="店铺区域id")
    message_datetime = Column(Integer, nullable=False, comment="用户id")
    message_type = Column(String(8), nullable=False,
                          default="00000000", comment="日志类型")
    message_title = Column(String(256), nullable=False, comment="消息标题")
    message_content = Column(String(1024), nullable=False, comment="日志内容")


class UserMessageRelationModel(BaseModel):
    """用户消息关系模型"""

    __tablename__ = BaseModel.table_prefix + "user_message_relation"
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, comment="用户id")
    message_id = Column(Integer, nullable=False, comment="消息id")
    is_read = Column(Integer, nullable=False, default=0, comment="是否已读")
    read_datetime = Column(DateTime, nullable=True, comment="读取时间")

    @classmethod
    async def get_message_page(cls, conditions, **other_config):
        """获取用户未读消息 -分页"""
        page = other_config.get('page', 1)
        page_size = other_config.get('page_size', 10)
        order_by = other_config.get("order_by")

        try:
            # 获取数据库连接

            # 构建过滤条件
            filters = cls.make_filters(conditions=conditions)

            # 计算总数
            count_stmt = select(func.count(cls.id)).select_from(
                cls).where(and_(*filters))
            total_result = await cls.db.execute(count_stmt)
            total = total_result.scalar_one()

            # 计算总页数 - 向上取整
            total_pages = (total + page_size - 1) // page_size

            # 构建子查询 - 获取分页后的关系表记录的message_id
            subquery = select(cls.message_id).where(and_(*filters))

            # 添加排序
            if order_by:
                for field_name, direction in order_by.items():
                    field = getattr(cls, field_name, None)
                    if field is not None:
                        subquery = subquery.order_by(
                            asc(field) if direction.lower() == "asc" else desc(field))

            # 添加分页
            subquery = subquery.offset(
                (page - 1) * page_size).limit(page_size).subquery()

            # 主查询 - 联合查询消息表和关系表
            main_query = select(UserMessageModel, cls).join(
                cls,
                and_(
                    UserMessageModel.id == cls.message_id,
                    UserMessageModel.id.in_(select([subquery]))  # 使用子查询的结果
                )
            )

            # 执行查询
            result = await cls.db().execute(main_query)
            records = result.all()

            # 处理结果
            items = []
            for message, relation in records:
                message_dict = UserMessageModel.to_dict(message)
                message_dict.pop('id')
                relation_dict = cls.to_dict(relation)
                # 合并两个字典，保留关系表的ID作为主键
                combined_dict = {**message_dict, **relation_dict}
                items.append(combined_dict)

            return {
                "total": total,
                "total_pages": total_pages,
                "page": page,
                "page_size": page_size,
                "items": items
            }
        except SQLAlchemyError as e:
            print(f"[get_not_read_message_page error] {e}")
            return {
                "total": 0,
                "total_pages": 0,
                "page": 1,
                "page_size": page_size,
                "items": []
            }

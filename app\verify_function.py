# -*- coding: utf-8 -*-
"""python 文件描述"""
from typing import Optional, Union
from fastapi import Request, HTTPException
from app.utils.redis_function import RedisFunction


def auth_error(code: int, msg: str):
    """创建认证错误异常"""
    return HTTPException(status_code=401, detail={"code": code, "msg": msg})


def get_token_value(token: str, redis: Optional[RedisFunction], request: Request) -> Optional[str]:
    """获取token对应的值"""
    return redis.get(token) if redis else request.session.get(token)


def _set_user_state(request: Request, user_type: str, value_id: Optional[str]) -> None:
    """设置用户状态的辅助函数"""
    user_id = int(value_id) if value_id else 0

    if user_type == "user":
        request.state.login_user_id = user_id
    elif user_type == "manager":
        request.state.login_manager_id = user_id
    else:
        raise auth_error(102003, "Invalid token type")


def _validate_token_and_set_state(
    request: Request,
    redis: Optional[RedisFunction],
    token: Optional[str],
    user_type: str,
    require_token: bool = True
) -> None:
    """验证token并设置状态的通用函数"""
    if require_token and not token:
        raise auth_error(102001, "Missing token")

    if token:
        full_token = f"{user_type.lower()}_{token}"
        value_id = get_token_value(full_token, redis, request)

        if require_token and not value_id:
            raise auth_error(102002, "Invalid token")
    else:
        value_id = None

    _set_user_state(request, user_type, value_id)


async def verify_token(
        request: Request,
        redis: Optional[RedisFunction] = None,
        verify_config: Union[dict, str, bool, None] = None) -> None:
    """验证接口请求"""
    # # todo 临时直接通过验证
    request.state.login_user_id = 1
    return
    # 跳过验证
    if not verify_config:
        return

    token = request.headers.get("Authorization")

    # 处理字符串类型配置
    if isinstance(verify_config, str):
        _validate_token_and_set_state(
            request=request,
            redis=redis,
            token=token,
            user_type=verify_config,
            require_token=True
        )

    # 处理字典类型配置
    elif isinstance(verify_config, dict):
        if 'type' not in verify_config:
            raise auth_error(102004, "Missing 'type' in verify_config")

        # 字典类型默认不强制要求token（根据原代码逻辑）
        _validate_token_and_set_state(
            request=request,
            redis=redis,
            token=token,
            user_type=verify_config['type'],
            require_token=False
        )

    else:
        raise auth_error(102005, "Invalid verify_config type")

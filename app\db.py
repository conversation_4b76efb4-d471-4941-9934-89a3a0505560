# -*- coding: utf-8 -*-
"""数据库基础连接文件"""
from contextvars import ContextVar
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.exc import OperationalError, DBAPIError, SQLAlchemyError
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from setting import DATABASE_HOST, DATABASE_USER, DATABASE_PSWD, DATABASE_NAME, DATABASE_PORT

_db_ctx: ContextVar[AsyncSession | None] = ContextVar("_db_ctx", default=None)

MYSQL_URL = (
    f"mysql+aiomysql://{DATABASE_USER}:{DATABASE_PSWD}@",
    f"{DATABASE_HOST}:{DATABASE_PORT}/{DATABASE_NAME}",
    "?charset=utf8mb4"
)
MYSQL_URL = "".join(MYSQL_URL)
engine = create_async_engine(MYSQL_URL, echo=False, future=True)
# 判断数据库账号密码是否正确
try:
    engine.connect()
except Exception as e:
    raise Exception(f"数据库账号密码错误{e}") from e

# 设置最长连接时间
AsyncSessionLocal = sessionmaker(
    engine,
    expire_on_commit=False,
    class_=AsyncSession
)


async def get_db():
    """获取数据库连接对象"""
    async with AsyncSessionLocal() as session:
        yield session


def bind_db(session: AsyncSession):
    """绑定数据库会话"""
    _db_ctx.set(session)


def get_bound_db() -> AsyncSession:
    """获取绑定的数据库会话"""
    db = _db_ctx.get()
    if db is None:
        raise RuntimeError("DB not bound. Ensure bind_db() is called.")
    return db


async def check_db_connection():
    """检查数据库连接"""
    try:
        async with engine.connect() as conn:
            await conn.execute(text("select 1"))
        return True
    except OperationalError as e:
        raise ConnectionError(f"数据库连接失败，可能是账号密码错误或数据库未启动：{e.orig}") from e
    except DBAPIError as e:
        raise RuntimeError(f"数据库连接成功但执行 SQL 出错：{e.orig}") from e
    except SQLAlchemyError as e:
        raise RuntimeError(f"SQLAlchemy 内部错误：{e}") from e
    except Exception as e:
        raise RuntimeError(f"未知错误：{type(e).__name__}: {e}") from e
